# Authentication

All API endpoints require authentication using JSON Web Tokens (JWT). This guide explains how to authenticate and use the API.

## Authentication Flow

1. **Obtain an access token**
   - Send a POST request to `/api/v1/auth/token` with your credentials
   - The token is valid for a limited time (default: 24 hours)

2. **Include the token in requests**
   - Add the token to the `Authorization` header
   - Format: `Bearer <your_access_token>`

## Obtaining an Access Token

### Request

```http
POST /api/v1/auth/token
Content-Type: application/x-www-form-urlencoded

username=your_username&password=your_password
```

### Response (Success)

```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 86400
}
```

### Response (Error)

```json
{
  "detail": "Incorrect username or password"
}
```

## Using the Access Token

Include the access token in the `Authorization` header of your requests:

```http
GET /api/v1/users/me
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Token Refresh

Access tokens expire after a set period. To get a new token:

1. Use the refresh token (if provided during login)
2. Or re-authenticate with username/password

```http
POST /api/v1/auth/refresh
Authorization: Bearer <refresh_token>
```

## API Keys

For server-to-server communication, you can generate an API key:

1. Log in to the web interface
2. Go to Settings > API Keys
3. Click "Generate New Key"
4. Use the key in the `X-API-Key` header

```http
GET /api/v1/data
X-API-Key: your_api_key_here
```

## Rate Limiting

- 100 requests per minute per IP address
- 1000 requests per hour per user
- 10,000 requests per day per API key

## Security Best Practices

1. Never expose your API keys in client-side code
2. Use HTTPS for all API requests
3. Rotate API keys regularly
4. Set appropriate permissions for each API key
5. Monitor your API usage for suspicious activity

## Error Handling

All error responses follow this format:

```json
{
  "detail": "Error message",
  "status_code": 400,
  "error": "error_code"
}
```

Common error codes:
- `401 Unauthorized`: Invalid or missing authentication
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error (check logs for details)
