# Quick Start Guide

Get up and running with NeuroRegulation AI in minutes.

## Prerequisites

- Python 3.9 or later
- pip (Python package manager)
- Git (for cloning the repository)

## Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/neuroregulation-ai.git
   cd neuroregulation-ai
   ```

2. **Create and activate a virtual environment**
   ```bash
   # On Windows
   python -m venv venv
   .\venv\Scripts\activate
   
   # On macOS/Linux
   python3 -m venv venv
   source venv/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

## Running the Application

1. **Start the development server**
   ```bash
   uvicorn app_advanced:app --reload
   ```

2. **Access the web interface**
   Open your browser and navigate to: [http://localhost:8000](http://localhost:8000)

3. **Upload your first dataset**
   - Click on "Upload Data" in the sidebar
   - Select a CSV file with your neuroregulation data
   - Configure the data import settings
   - Click "Import"

4. **Explore the dashboard**
   - View real-time visualizations
   - Explore different analysis tools
   - Configure model parameters

## Example: Training Your First Model

1. **Prepare your data**
   - Ensure your data is in CSV format
   - Include relevant features (HRV, skin temp, etc.)
   - Make sure the target variable is specified

2. **Start training**
   - Navigate to the "Models" section
   - Click "New Model"
   - Select your dataset and target variable
   - Configure training parameters
   - Click "Start Training"

3. **Monitor training progress**
   - View real-time metrics
   - Monitor loss and accuracy
   - Stop training if needed

4. **Evaluate the model**
   - Review performance metrics
   - Analyze feature importance
   - Test with new data

## Next Steps

- Explore the [User Guide](../user-guide/README.md) for detailed usage instructions
- Check out the [API Reference](../api-reference/README.md) for integrating with other tools
- Read the [Developer Guide](../developer-guide/README.md) for contributing to the project

## Getting Help

- [FAQ](../user-guide/faq/README.md)
- [GitHub Issues](https://github.com/yourusername/neuroregulation-ai/issues)
- [Community Forum](https://community.example.com)
