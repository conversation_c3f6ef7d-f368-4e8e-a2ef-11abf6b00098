import os
import numpy as np
import pandas as pd
import streamlit as st
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import plotly.graph_objects as go
from datetime import datetime

class NeuroRegulationModel(nn.Module):
    """Neural network model for neuroregulation prediction."""
    def __init__(self, input_size, hidden_size=64, output_size=1):
        super(NeuroRegulationModel, self).__init__()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.2)
        self.fc2 = nn.Linear(hidden_size, hidden_size//2)
        self.fc3 = nn.Linear(hidden_size//2, output_size)
        
    def forward(self, x):
        x = self.fc1(x)
        x = self.relu(x)
        x = self.dropout(x)
        x = self.fc2(x)
        x = self.relu(x)
        x = self.fc3(x)
        return x

def load_data(file_path):
    """Load and preprocess the neuroregulation data."""
    try:
        df = pd.read_csv(file_path)
        # Basic preprocessing
        df = df.drop_duplicates()
        df = df.dropna()
        return df
    except Exception as e:
        st.error(f"Error loading data: {str(e)}")
        return None

def prepare_data(df, target_column, test_size=0.2, random_state=42):
    """Prepare data for training and testing."""
    X = df.drop(columns=[target_column])
    y = df[target_column]
    
    # Convert to numpy arrays
    X = X.values
    y = y.values.reshape(-1, 1)
    
    # Split the data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state
    )
    
    # Scale the features
    scaler = StandardScaler()
    X_train = scaler.fit_transform(X_train)
    X_test = scaler.transform(X_test)
    
    # Convert to PyTorch tensors
    X_train = torch.FloatTensor(X_train)
    X_test = torch.FloatTensor(X_test)
    y_train = torch.FloatTensor(y_train)
    y_test = torch.FloatTensor(y_test)
    
    return X_train, X_test, y_train, y_test, scaler

def train_model(model, X_train, y_train, epochs=100, batch_size=32, learning_rate=0.001):
    """Train the neuroregulation model."""
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)
    
    # Create DataLoader
    train_data = TensorDataset(X_train, y_train)
    train_loader = DataLoader(train_data, batch_size=batch_size, shuffle=True)
    
    # Training loop
    for epoch in range(epochs):
        for inputs, targets in train_loader:
            # Forward pass
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            
            # Backward and optimize
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
        if (epoch+1) % 10 == 0:
            print(f'Epoch [{epoch+1}/{epochs}], Loss: {loss.item():.4f}')
    
    return model

def evaluate_model(model, X_test, y_test):
    """Evaluate the trained model."""
    with torch.no_grad():
        y_pred = model(X_test)
        mse = nn.MSELoss()(y_pred, y_test)
        return mse.item()

def plot_results(y_true, y_pred, title="Predicted vs Actual"):
    """Plot predicted vs actual values."""
    fig = go.Figure()
    fig.add_trace(go.Scatter(
        y=y_true.flatten(),
        mode='lines',
        name='Actual',
        line=dict(color='blue')
    ))
    fig.add_trace(go.Scatter(
        y=y_pred.flatten(),
        mode='lines',
        name='Predicted',
        line=dict(color='red', dash='dash')
    ))
    fig.update_layout(
        title=title,
        xaxis_title='Samples',
        yaxis_title='Value',
        showlegend=True
    )
    return fig

def main():
    st.title("Neuroregulation Analysis Tool")
    st.write("Analyze and predict neuroregulation patterns for optimal human performance")
    
    # Sidebar for user inputs
    st.sidebar.header("Settings")
    uploaded_file = st.sidebar.file_uploader("Upload your dataset (CSV)", type="csv")
    
    if uploaded_file is not None:
        # Load and display data
        df = pd.read_csv(uploaded_file)
        st.subheader("Dataset Preview")
        st.write(df.head())
        
        # Get target column
        target_col = st.selectbox("Select target column", df.columns)
        
        if st.button("Train Model"):
            with st.spinner('Training in progress...'):
                # Prepare data
                X_train, X_test, y_train, y_test, scaler = prepare_data(df, target_col)
                
                # Initialize model
                input_size = X_train.shape[1]
                model = NeuroRegulationModel(input_size=input_size)
                
                # Train model
                model = train_model(model, X_train, y_train)
                
                # Evaluate model
                mse = evaluate_model(model, X_test, y_test)
                st.success(f"Model trained successfully! MSE: {mse:.4f}")
                
                # Make predictions
                with torch.no_grad():
                    y_pred = model(X_test).numpy()
                
                # Plot results
                fig = plot_results(y_test.numpy(), y_pred, "Predicted vs Actual Values")
                st.plotly_chart(fig, use_container_width=True)
                
                # Feature importance (simple implementation)
                st.subheader("Feature Importance")
                # Add your feature importance calculation here
                
    else:
        st.info("Please upload a CSV file to get started.")

if __name__ == "__main__":
    main()
