"""
Configuration settings for the NeuroRegulation AI application.
"""
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
from pydantic import BaseSettings, Field, validator
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Base directory
BASE_DIR = Path(__file__).resolve().parent

class Settings(BaseSettings):
    # Application settings
    APP_NAME: str = "NeuroRegulation AI"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = os.getenv("DEBUG", "False").lower() in ("true", "1", "t")
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-here")
    
    # Server settings
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", "8000"))
    WORKERS: int = int(os.getenv("WORKERS", "1"))
    RELOAD: bool = os.getenv("RELOAD", str(DEBUG)).lower() in ("true", "1", "t")
    
    # CORS settings
    CORS_ORIGINS: List[str] = os.getenv("CORS_ORIGINS", "*").split(",")
    
    # Database settings
    DATABASE_URL: str = os.getenv("DATABASE_URL", f"sqlite:///{BASE_DIR}/data/neuroreg.db")
    TEST_DATABASE_URL: str = os.getenv("TEST_DATABASE_URL", "sqlite:///:memory:")
    
    # Model settings
    MODEL_PATH: Path = BASE_DIR / "models" / "neuro_reg_model.pt"
    SCALER_PATH: Path = BASE_DIR / "models" / "scaler.pkl"
    MODEL_INPUT_FEATURES: List[str] = [
        'heart_rate', 'hrv', 'respiration_rate', 'skin_temp', 
        'activity_level', 'sleep_quality', 'stress_level', 'cognitive_load'
    ]
    TARGET_FEATURE: str = 'neuroregulation_score'
    
    # Training settings
    DEFAULT_EPOCHS: int = 100
    DEFAULT_BATCH_SIZE: int = 32
    DEFAULT_LEARNING_RATE: float = 0.001
    TEST_SIZE: float = 0.2
    RANDOM_STATE: int = 42
    
    # Feature engineering settings
    WINDOW_SIZE: int = 10  # For time series features
    ROLLING_WINDOWS: List[int] = [5, 10, 20]  # For rolling statistics
    
    # API settings
    API_PREFIX: str = "/api/v1"
    TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7 days
    
    # WebSocket settings
    WS_RECONNECT_DELAY: float = 1.0  # seconds
    WS_MAX_MESSAGE_SIZE: int = 1024 * 1024  # 1MB
    
    # Monitoring settings
    ENABLE_TELEMETRY: bool = os.getenv("ENABLE_TELEMETRY", "False").lower() in ("true", "1", "t")
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    
    # Cache settings
    CACHE_TTL: int = 300  # 5 minutes
    
    # Security settings
    JWT_ALGORITHM: str = "HS256"
    
    class Config:
        case_sensitive = True
        env_file = ".env"
        env_file_encoding = "utf-8"
    
    @validator("CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: str | List[str]) -> List[str] | str:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

# Create instance
settings = Settings()

# Ensure directories exist
for directory in ["data", "models", "static"]:
    (BASE_DIR / directory).mkdir(exist_ok=True)

# Export settings
__all__ = ["settings"]