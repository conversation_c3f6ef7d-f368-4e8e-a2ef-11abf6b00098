{"name": "neuroreg-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "format": "prettier --write ."}, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.13.0", "@mui/x-data-grid": "^6.4.0", "@mui/x-date-pickers": "^6.3.1", "@nivo/line": "^0.88.0", "@tanstack/react-query": "^4.29.5", "axios": "^1.4.0", "date-fns": "^2.30.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.0", "react-router-dom": "^6.11.1", "recharts": "^2.7.2", "zod": "^3.21.4"}, "devDependencies": {"@types/node": "^20.2.5", "@types/react": "^18.2.6", "@types/react-dom": "^18.2.4", "@typescript-eslint/eslint-plugin": "^5.59.7", "@typescript-eslint/parser": "^5.59.7", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.14", "eslint": "^8.41.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "postcss": "^8.4.23", "prettier": "^2.8.8", "sass": "^1.62.1", "tailwindcss": "^3.3.2", "typescript": "^5.0.4", "vite": "^4.3.5", "vite-plugin-svgr": "^2.4.0"}}