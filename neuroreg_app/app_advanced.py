import os
import asyncio
import json
import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging
import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import torch
import torch.nn as nn
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from pydantic import BaseModel, Field
from loguru import logger
from dataclasses import dataclass
import uuid
import joblib
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI
app = FastAPI(
    title="NeuroRegulation AI",
    description="Advanced Neuroregulation Analysis and Prediction Platform",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
os.makedirs("static", exist_ok=True)
app.mount("/static", StaticFiles(directory="static"), name="static")

# Data models
class NeuroDataPoint(BaseModel):
    timestamp: datetime
    heart_rate: float
    hrv: float
    respiration_rate: float
    skin_temp: float
    activity_level: float = Field(..., ge=0, le=1)
    sleep_quality: float = Field(..., ge=0, le=10)
    stress_level: float = Field(..., ge=0, le=10)
    cognitive_load: float = Field(..., ge=0, le=10)
    neuroregulation_score: Optional[float] = Field(None, ge=0, le=100)

class TrainingConfig(BaseModel):
    epochs: int = 100
    batch_size: int = 32
    learning_rate: float = 0.001
    test_size: float = 0.2
    random_state: int = 42

# Neural Network Model
class NeuroRegulationModel(nn.Module):
    def __init__(self, input_size: int, hidden_size: int = 128, output_size: int = 1):
        super().__init__()
        self.model = nn.Sequential(
            nn.Linear(input_size, hidden_size),
            nn.LeakyReLU(),
            nn.Dropout(0.3),
            nn.LayerNorm(hidden_size),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.LeakyReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_size // 2, hidden_size // 4),
            nn.LeakyReLU(),
            nn.Linear(hidden_size // 4, output_size)
        )
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.model(x)

# WebSocket Manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, client_id: str):
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info(f"Client {client_id} connected")

    def disconnect(self, client_id: str):
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            logger.info(f"Client {client_id} disconnected")

    async def send_personal_message(self, message: str, client_id: str):
        if client_id in self.active_connections:
            await self.active_connections[client_id].send_text(message)

manager = ConnectionManager()

# Global variables
scaler = StandardScaler()
model: Optional[NeuroRegulationModel] = None
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
MODEL_PATH = "models/neuro_reg_model.pt"
SCALER_PATH = "models/scaler.pkl"

# Ensure models directory exists
os.makedirs("models", exist_ok=True)

# API Endpoints
@app.get("/", response_class=HTMLResponse)
async def get_dashboard():
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>NeuroRegulation AI Dashboard</title>
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
        <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    </head>
    <body class="bg-gray-100">
        <div id="app" class="min-h-screen">
            <!-- Navigation -->
            <nav class="bg-indigo-700 text-white p-4">
                <div class="container mx-auto flex justify-between items-center">
                    <h1 class="text-2xl font-bold">NeuroRegulation AI</h1>
                    <div class="space-x-4">
                        <button id="connectBtn" class="bg-green-500 hover:bg-green-600 px-4 py-2 rounded">
                            Connect
                        </button>
                    </div>
                </div>
            </nav>

            <!-- Main Content -->
            <div class="container mx-auto p-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Data Upload Card -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h2 class="text-xl font-semibold mb-4">Upload Data</h2>
                        <input type="file" id="fileInput" class="mb-4">
                        <button id="uploadBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded w-full">
                            Upload and Analyze
                        </button>
                    </div>

                    <!-- Real-time Metrics -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h2 class="text-xl font-semibold mb-4">Real-time Metrics</h2>
                        <div id="metrics" class="space-y-4">
                            <!-- Metrics will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Model Controls -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h2 class="text-xl font-semibold mb-4">Model Controls</h2>
                        <div class="space-y-4">
                            <button id="trainBtn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded w-full">
                                Train Model
                            </button>
                            <button id="predictBtn" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded w-full">
                                Make Prediction
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold mb-4">Neuroregulation Over Time</h3>
                        <div id="timeSeriesChart"></div>
                    </div>
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold mb-4">Feature Importance</h3>
                        <div id="featureImportanceChart"></div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            const ws = new WebSocket('ws://' + window.location.host + '/ws');
            const clientId = 'web_' + Math.random().toString(36).substr(2, 9);
            
            // Connection management
            let isConnected = false;
            
            document.getElementById('connectBtn').addEventListener('click', () => {
                if (isConnected) {
                    ws.close();
                    document.getElementById('connectBtn').textContent = 'Connect';
                    document.getElementById('connectBtn').classList.remove('bg-red-500', 'hover:bg-red-600');
                    document.getElementById('connectBtn').classList.add('bg-green-500', 'hover:bg-green-600');
                    isConnected = false;
                } else {
                    ws.send(JSON.stringify({
                        type: 'connect',
                        client_id: clientId
                    }));
                    document.getElementById('connectBtn').textContent = 'Disconnect';
                    document.getElementById('connectBtn').classList.remove('bg-green-500', 'hover:bg-green-600');
                    document.getElementById('connectBtn').classList.add('bg-red-500', 'hover:bg-red-600');
                    isConnected = true;
                }
            });
            
            // WebSocket message handling
            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                console.log('Received:', data);
                
                if (data.type === 'metrics_update') {
                    updateMetrics(data.metrics);
                } else if (data.type === 'chart_update') {
                    updateChart(data.chart_id, data.data);
                } else if (data.type === 'training_progress') {
                    updateTrainingProgress(data.progress);
                }
            };
            
            // File upload handling
            document.getElementById('uploadBtn').addEventListener('click', async () => {
                const fileInput = document.getElementById('fileInput');
                if (fileInput.files.length === 0) {
                    alert('Please select a file first');
                    return;
                }
                
                const file = fileInput.files[0];
                const formData = new FormData();
                formData.append('file', file);
                
                try {
                    const response = await fetch('/api/upload', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    if (response.ok) {
                        alert('File uploaded and processed successfully');
                        // Trigger data visualization
                        ws.send(JSON.stringify({
                            type: 'request_chart_update',
                            chart_id: 'time_series',
                            client_id: clientId
                        }));
                    } else {
                        alert(`Error: ${result.detail}`);
                    }
                } catch (error) {
                    console.error('Error uploading file:', error);
                    alert('Error uploading file');
                }
            });
            
            // Train model button
            document.getElementById('trainBtn').addEventListener('click', () => {
                ws.send(JSON.stringify({
                    type: 'train_model',
                    client_id: clientId,
                    config: {
                        epochs: 100,
                        batch_size: 32,
                        learning_rate: 0.001
                    }
                }));
            });
            
            // Update metrics display
            function updateMetrics(metrics) {
                const metricsDiv = document.getElementById('metrics');
                metricsDiv.innerHTML = '';
                
                for (const [key, value] of Object.entries(metrics)) {
                    const metricElement = document.createElement('div');
                    metricElement.className = 'bg-gray-100 p-3 rounded';
                    metricElement.innerHTML = `
                        <div class="flex justify-between">
                            <span class="font-medium">${key.replace('_', ' ').toUpperCase()}:</span>
                            <span class="font-bold">${value}</span>
                        </div>
                    `;
                    metricsDiv.appendChild(metricElement);
                }
            }
            
            // Update chart
            function updateChart(chartId, chartData) {
                if (chartId === 'time_series') {
                    Plotly.newPlot('timeSeriesChart', chartData.data, chartData.layout);
                } else if (chartId === 'feature_importance') {
                    Plotly.newPlot('featureImportanceChart', chartData.data, chartData.layout);
                }
            }
            
            // Update training progress
            function updateTrainingProgress(progress) {
                console.log('Training progress:', progress);
                // Update UI with training progress
            }
        </script>
    </body>
    </html>
    """

# WebSocket endpoint
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    client_id = str(uuid.uuid4())
    await manager.connect(websocket, client_id)
    
    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)
            
            if message["type"] == "connect":
                await manager.send_personal_message(
                    json.dumps({"status": "connected", "client_id": client_id}),
                    client_id
                )
            
            elif message["type"] == "train_model":
                # Start training in background
                asyncio.create_task(train_model_async(message.get("config", {}), client_id))
            
            # Handle other message types...
            
    except WebSocketDisconnect:
        manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"WebSocket error: {str(e)}")
        manager.disconnect(client_id)

# API Endpoints
@app.post("/api/upload")
async def upload_file(file: UploadFile = File(...)):
    try:
        # Save and process the uploaded file
        contents = await file.read()
        df = pd.read_csv(io.BytesIO(contents))
        
        # Validate and process data
        # ...
        
        return {"status": "success", "message": "File processed successfully"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/api/train")
async def train_model_endpoint(config: TrainingConfig):
    try:
        # Start training in background
        asyncio.create_task(train_model_async(config.dict()))
        return {"status": "training_started", "message": "Model training started"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Background tasks
async def train_model_async(config: Dict[str, Any], client_id: str = None):
    """Train model asynchronously and send updates via WebSocket."""
    try:
        # Load and prepare data
        # ...
        
        # Initialize model
        model = NeuroRegulationModel(input_size=X_train.shape[1])
        model.to(device)
        
        # Training loop
        for epoch in range(config['epochs']):
            # Training steps...
            
            # Send progress update
            if client_id and epoch % 10 == 0:
                progress = {
                    'epoch': epoch,
                    'total_epochs': config['epochs'],
                    'loss': loss.item(),
                    'status': 'training'
                }
                await manager.send_personal_message(
                    json.dumps({
                        'type': 'training_progress',
                        'progress': progress
                    }),
                    client_id
                )
                
        # Save model
        torch.save(model.state_dict(), MODEL_PATH)
        joblib.dump(scaler, SCALER_PATH)
        
        # Send completion message
        if client_id:
            await manager.send_personal_message(
                json.dumps({
                    'type': 'training_complete',
                    'message': 'Model training completed successfully',
                    'model_path': MODEL_PATH
                }),
                client_id
            )
            
    except Exception as e:
        logger.error(f"Training error: {str(e)}")
        if client_id:
            await manager.send_personal_message(
                json.dumps({
                    'type': 'training_error',
                    'error': str(e)
                }),
                client_id
            )

if __name__ == "__main__":
    uvicorn.run("app_advanced:app", host="0.0.0.0", port=8000, reload=True)
