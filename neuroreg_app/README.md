# Neuroregulation Analysis Tool

A powerful application for analyzing and predicting neuroregulation patterns to optimize human performance. This tool uses machine learning to analyze physiological and behavioral data to provide insights into neuroregulation states.

## Features

- **Data Analysis**: Upload and visualize your neuroregulation data
- **Machine Learning**: Train predictive models using PyTorch
- **Interactive Visualizations**: Explore your data with interactive plots
- **Real-time Monitoring**: Track neuroregulation metrics over time
- **Performance Optimization**: Get recommendations for optimal performance based on your data

## Installation

1. Clone this repository:
   ```bash
   git clone [your-repository-url]
   cd neuroreg_app
   ```

2. Install the required packages:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

1. Run the application:
   ```bash
   streamlit run main.py
   ```

2. Open your web browser and navigate to the URL shown in the terminal (usually http://localhost:8501)

3. Upload your dataset (CSV format) using the sidebar

4. Select the target variable you want to predict

5. Click "Train Model" to start the analysis

## Sample Data

A sample dataset is provided in `sample_data/neuroregulation_sample.csv` with the following columns:

- `timestamp`: Date and time of measurement
- `heart_rate`: Heart rate in BPM
- `hrv`: Heart rate variability
- `respiration_rate`: Breaths per minute
- `skin_temp`: Skin temperature in Celsius
- `activity_level`: Physical activity level (0-1)
- `sleep_quality`: Self-reported sleep quality (1-10)
- `stress_level`: Perceived stress level (1-10)
- `cognitive_load`: Mental workload (1-10)
- `neuroregulation_score`: Target variable (0-100)

## Customization

You can customize the model architecture and training parameters in `main.py`:

- Adjust the `NeuroRegulationModel` class to modify the neural network architecture
- Change training parameters in the `train_model` function
- Add new visualization components as needed

## Requirements

See `requirements.txt` for the complete list of dependencies.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
