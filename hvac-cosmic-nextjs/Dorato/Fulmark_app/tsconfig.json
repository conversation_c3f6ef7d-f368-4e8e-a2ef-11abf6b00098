/* eslint-disable jsonc/sort-keys */
{
  "compilerOptions": {
    // ======================================================================
    // Language & Environment
    // Defines JavaScript version and runtime environment
    // ======================================================================
    "target": "ES2017",
    "module": "esnext",
    "lib": ["dom", "dom.iterable", "esnext"],
    "moduleResolution": "bundler",
    "isolatedModules": true,

    // ======================================================================
    // Type Safety - Foundation
    // Core type checking settings for a robust codebase
    // ======================================================================
    "strict": true,
    "alwaysStrict": true,
    "strictNullChecks": true,
    "noImplicitAny": true,
    "noImplicitThis": true,

    // ======================================================================
    // Type Safety - Advanced
    // Additional checks for higher code quality
    // ======================================================================
    "noUncheckedIndexedAccess": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "allowUnreachableCode": false,
    "useUnknownInCatchVariables": true,
    "noImplicitOverride": true,

    // ======================================================================
    // Interoperability
    // Settings for working with different file types and modules
    // ======================================================================
    "allowJs": true,
    "checkJs": true,
    "esModuleInterop": true,
    "resolveJsonModule": true,

    // ======================================================================
    // Build & Performance
    // Settings that affect compilation output and build performance
    // ======================================================================
    "skipLibCheck": true,
    "removeComments": true,
    "preserveConstEnums": true,
    "forceConsistentCasingInFileNames": true,

    // ======================================================================
    // Project Structure
    // Configure import paths and module resolution
    // ======================================================================
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/public/*": ["./public/*"]
    },

    // ======================================================================
    // Next.js Project Configuration
    // Controls settings specific to Next.js framework
    // ======================================================================
    "jsx": "preserve", // Preserve JSX for Next.js transformation
    "incremental": true, // Enable faster incremental builds
    "noEmit": true, // Skip emitting files (Next.js handles this)
    "plugins": [{ "name": "next" }] // Enable Next.js TypeScript plugin
  },

  // Files to include/exclude from the project
  "exclude": ["node_modules", "**/*.spec.ts", "**/*.e2e.ts"],
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".storybook/*.ts", ".next/types/**/*.ts", "**/*.mts"]
}
