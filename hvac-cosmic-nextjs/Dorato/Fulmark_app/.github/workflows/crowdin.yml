name: Crowdin Action

on:
  push:
    branches: [main] # Run on push to the main branch
  schedule:
    - cron: '0 5 * * *' # Run every day at 5am
  workflow_dispatch: # Run manually

jobs:
  synchronize-with-crowdin:
    name: Synchronize with <PERSON><PERSON>
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: crowdin action
        uses: crowdin/github-action@v2
        with:
          upload_sources: true
          upload_translations: true
          download_translations: true
          localization_branch_name: l10n_crowdin_translations
          create_pull_request: true
          pull_request_title: New Crowdin Translations
          pull_request_body: 'New Crowdin translations by [Crowdin GH Action](https://github.com/crowdin/github-action)'
          pull_request_base_branch_name: main
          commit_message: 'chore: new Crowdin translations by GitHub Action'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CROWDIN_PROJECT_ID: ${{ secrets.CROWDIN_PROJECT_ID }}
          CROWDIN_PERSONAL_TOKEN: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}
