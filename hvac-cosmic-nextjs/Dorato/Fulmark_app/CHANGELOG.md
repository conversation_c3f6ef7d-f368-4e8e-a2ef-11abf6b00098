# [3.68.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.67.3...v3.68.0) (2025-06-02)


### Bug Fixes

* add priority for lefthook scripts ([875266a](https://github.com/ixartz/Next-js-Boilerplate/commit/875266a6bcd4e8d0f14f88202d54072ee30e7ab8))
* change default screenshot directory for vitest browser mode ([718b16a](https://github.com/ixartz/Next-js-Boilerplate/commit/718b16a471c6a7c1ea8a4090bdcbd77f8cca75b7))
* ensure fixed files are staged in pre-commit linting ([d0de38c](https://github.com/ixartz/Next-js-Boilerplate/commit/d0de38c7923af3090886116821aecfb0b6a0dc04))


### Features

* replace husky + lint-staged by lefthook ([ff7babc](https://github.com/ixartz/Next-js-Boilerplate/commit/ff7babc0954bfd3fe8c2486571e3d72f3c7e796c))
* use vitest browser mode instead of [@testing-library](https://github.com/testing-library) ([f22cb49](https://github.com/ixartz/Next-js-Boilerplate/commit/f22cb4937a0b7b3d4c0882ef01d1e55da7a4d208))

## [3.67.3](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.67.2...v3.67.3) (2025-05-31)


### Bug Fixes

* ensure page refreshes to reflect new locale in LocaleSwitcher ([0707e50](https://github.com/ixartz/Next-js-Boilerplate/commit/0707e508a00d12fb69f5232a0296d54814e95fc2))
* update type definitions for next-intl module ([b1381b1](https://github.com/ixartz/Next-js-Boilerplate/commit/b1381b1666bdb3d53e45779183fa2fbf9f094f8f))

## [3.67.2](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.67.1...v3.67.2) (2025-05-09)


### Bug Fixes

* group dependabot all updates into a single PR ([ba74448](https://github.com/ixartz/Next-js-Boilerplate/commit/ba7444821f896d4d02f8f17124d0e1adb20fe922))
* typo in dependabot.yml config file ([2c6933d](https://github.com/ixartz/Next-js-Boilerplate/commit/2c6933d73d24bd6b8f6b45207b5550611372fad1))

## [3.67.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.67.0...v3.67.1) (2025-05-09)


### Bug Fixes

* update dependabot cron schedule to run at 00:45 UTC ([d59ed9f](https://github.com/ixartz/Next-js-Boilerplate/commit/d59ed9f889ecce77d369246399f2058450c6664a))

# [3.67.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.66.6...v3.67.0) (2025-05-08)


### Bug Fixes

* update dependabot schedule to check for npm updates at 1 AM ([1b5358b](https://github.com/ixartz/Next-js-Boilerplate/commit/1b5358b5e019d8c1b5ff319eaeecefea6d3b9273))
* update robots.txt to disallow access to the dashboard ([5f510d9](https://github.com/ixartz/Next-js-Boilerplate/commit/5f510d9c0acc089a51f31f85e00df7a155621b7e))


### Features

* add dependabot configuration for npm package updates ([7891616](https://github.com/ixartz/Next-js-Boilerplate/commit/789161629c1cd473fec6088980499233468097ec))

## [3.66.6](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.66.5...v3.66.6) (2025-05-07)


### Bug Fixes

* disable sentry when debugging with vscode ([67309fe](https://github.com/ixartz/Next-js-Boilerplate/commit/67309fe21659659972b621d2113b2a8eefca52f1))
* in instrumentation client, add environment variable to disable sentry ([7963565](https://github.com/ixartz/Next-js-Boilerplate/commit/79635655cb3e09494689fcddfe852f90faa3cad1))

## [3.66.5](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.66.4...v3.66.5) (2025-05-04)


### Bug Fixes

* add next-intl 4 and rewrite next.config.ts ([b468271](https://github.com/ixartz/Next-js-Boilerplate/commit/b468271f9922f95497c5b46733a194023c3c4458))
* remove suppressHydrationWarning not needed anymore ([be70570](https://github.com/ixartz/Next-js-Boilerplate/commit/be705709eb65986bde67097af5d4cdf01f5915fc))
* update configuration after updating to next-intl 4 ([0b60f1c](https://github.com/ixartz/Next-js-Boilerplate/commit/0b60f1c492df8d89f3fe9d22aefbdb1b28410b20))

## [3.66.4](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.66.3...v3.66.4) (2025-05-03)


### Bug Fixes

* add noImplicitOverride option to tsconfig.json for stricter type checking ([0b23f5d](https://github.com/ixartz/Next-js-Boilerplate/commit/0b23f5dc4a9b3252954b0c160befa7a1d969f12d))
* use autoImportSpecifierExcludeRegexes instead of autoImportFileExcludePatterns in TS config ([19d783f](https://github.com/ixartz/Next-js-Boilerplate/commit/19d783f866ee363cc36f892910b03bbb00005178))

## [3.66.3](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.66.2...v3.66.3) (2025-04-25)


### Bug Fixes

* enhance ESLint configuration with comments and structure improvements ([16da265](https://github.com/ixartz/Next-js-Boilerplate/commit/16da265927ca869f43a22a90bc9c6f1f14dadead))
* regenerate the migrations folder ([64ef5ec](https://github.com/ixartz/Next-js-Boilerplate/commit/64ef5ec4534a7580834392059c798776cac02218))
* remove csstools.postcss from VSCode extensions recommendations ([a9f9510](https://github.com/ixartz/Next-js-Boilerplate/commit/a9f95105350c5b44dea405bdc4e82d34b15a7e29))
* remove verbatimModuleSyntax to support Checkly configuration ([f039e90](https://github.com/ixartz/Next-js-Boilerplate/commit/f039e90b61a45cec9e9e0b361baf8dd5449aacbc))
* reorganize tsconfig.json with improved comments and structure ([9a87712](https://github.com/ixartz/Next-js-Boilerplate/commit/9a87712d8aed7f4b686f7aaa581196afaab866b2))
* update ESLint settings for code actions on save ([afcbced](https://github.com/ixartz/Next-js-Boilerplate/commit/afcbced2ac72f3524da6a6c6d024a381566f6fbd))
* update gitignore files ([7a91472](https://github.com/ixartz/Next-js-Boilerplate/commit/7a91472a78be83ab93c34b2ad9a4b3891d0ee783))
* update tsconfig.json to include vitest types and refine file exclusions ([6cb051d](https://github.com/ixartz/Next-js-Boilerplate/commit/6cb051d6ec25e8edcc3a4c5f619519afa0cbea77))

## [3.66.2](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.66.1...v3.66.2) (2025-04-12)


### Bug Fixes

* update hookform/resolvers to v5, infer automatically type for useForm ([ca08876](https://github.com/ixartz/Next-js-Boilerplate/commit/ca088767bf2e980913547f94fc172f1ca93f1ae1))

## [3.66.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.66.0...v3.66.1) (2025-04-09)


### Bug Fixes

* upgrade to Next.js 15.3 ([426b1c2](https://github.com/ixartz/Next-js-Boilerplate/commit/426b1c2c5a6338be98357d13278191665ec3866b))

# [3.66.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.65.2...v3.66.0) (2025-04-01)


### Features

* add initial configuration for CodeRabbit integration ([10f9a3a](https://github.com/ixartz/Next-js-Boilerplate/commit/10f9a3ac20513cbe2e86e7750e84e339277c806a))

## [3.65.2](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.65.1...v3.65.2) (2025-03-26)


### Bug Fixes

* replace Env.ARCJET_KEY with process.env.ARCJET_KEY in libs/Arcjet.ts to reduce middlware size ([b82f773](https://github.com/ixartz/Next-js-Boilerplate/commit/b82f77387b144ddd4716bd8749560a92d3563e1e))
* replace Env.ARCJET_KEY with process.env.ARCJET_KEY to reduce middlware size ([ae20b29](https://github.com/ixartz/Next-js-Boilerplate/commit/ae20b2906065550649680cde1a22d85f65b9fc52))
* update comments to clarify use of process.env for reducing middleware bundle size ([dee7d34](https://github.com/ixartz/Next-js-Boilerplate/commit/dee7d34b10660ad07bbe7b0710d35f2fd2a23340))

## [3.65.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.65.0...v3.65.1) (2025-03-26)


### Bug Fixes

* **arcjet:** move Arcjet to middleware to prevent re-renders in layout ([8724160](https://github.com/ixartz/Next-js-Boilerplate/commit/872416014a0e3fe9902bcde15af569d0d8ece323))

# [3.65.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.64.1...v3.65.0) (2025-03-24)


### Bug Fixes

* remove middleware changes ([841b638](https://github.com/ixartz/Next-js-Boilerplate/commit/841b638b02f0b18031a39408d03b1cca824d9cf2))


### Features

* add posthog integration for analytics ([097ab8f](https://github.com/ixartz/Next-js-Boilerplate/commit/097ab8f6fb175631ef4c7156c033383c2afad82c))

## [3.64.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.64.0...v3.64.1) (2025-03-13)


### Bug Fixes

* **utils/AppConfig:** update import path for LocalePrefixMode to use a package-level reference ([354b5a5](https://github.com/ixartz/Next-js-Boilerplate/commit/354b5a51a3f86a29aef569a6e6dd592b86942050)), closes [#367](https://github.com/ixartz/Next-js-Boilerplate/issues/367)

# [3.64.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.63.0...v3.64.0) (2025-02-27)


### Features

* update to Next.js 15.2 ([f8f4caf](https://github.com/ixartz/Next-js-Boilerplate/commit/f8f4cafac32d7ac9942b08d7b103725c8702a87c))

# [3.63.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.62.1...v3.63.0) (2025-02-25)


### Bug Fixes

* move out block ([7ec1aee](https://github.com/ixartz/Next-js-Boilerplate/commit/7ec1aee87f7fde769cf2d44fe3346cb8e2c5699c))


### Features

* **middleware:** add handling for sitemap and robots files ([efa279b](https://github.com/ixartz/Next-js-Boilerplate/commit/efa279b64320df6e258cb2e85f1ade9ce5d76b7f))

## [3.62.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.62.0...v3.62.1) (2025-02-22)


### Bug Fixes

* add Sevalla sponsorship to Sponsors component and README ([316b369](https://github.com/ixartz/Next-js-Boilerplate/commit/316b369c1764627ee139a9a2ba18695b4cba8fe7))

# [3.62.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.61.0...v3.62.0) (2025-02-12)


### Features

* enable turbo in next.js dev mode ([97d7b32](https://github.com/ixartz/Next-js-Boilerplate/commit/97d7b32e98e27640416fca26f13eb5c525252685))

# [3.61.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.60.5...v3.61.0) (2025-01-24)


### Bug Fixes

* apply css classes after upgrading to tailwind css 4 ([70f5a87](https://github.com/ixartz/Next-js-Boilerplate/commit/70f5a878e4c9313235d578497a22b1bef5df9203))


### Features

* update to Tailwind CSS v4 ([5ac5331](https://github.com/ixartz/Next-js-Boilerplate/commit/5ac5331818e31be4c197dea4430af57a55753708))
* upgrade to Vitest 3 ([3e904f5](https://github.com/ixartz/Next-js-Boilerplate/commit/3e904f568ffadf174cf4cb4794350676af1435e8))


### Reverts

* add back eslint-plugin-jsx-a11y in eslint config ([0a621ab](https://github.com/ixartz/Next-js-Boilerplate/commit/0a621abc5ac26b859bc4139f8592eeacfe0bd213))

## [3.60.5](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.60.4...v3.60.5) (2024-12-20)


### Bug Fixes

* add new message in hello component ([5ef2fc5](https://github.com/ixartz/Next-js-Boilerplate/commit/5ef2fc52f0cc3d7b6a8754832f4dda9127098df5))
* use new vitest vscode setting for preventing automatic opening of the test results ([36027db](https://github.com/ixartz/Next-js-Boilerplate/commit/36027dba2d65fd5faf30504cae88735f438b20cd))

## [3.60.4](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.60.3...v3.60.4) (2024-12-16)


### Bug Fixes

* remove custom framework configuration file for i18n ally ([a681b13](https://github.com/ixartz/Next-js-Boilerplate/commit/a681b13e7ddd78afd1cc9b2c02d9797d71c8f992))

## [3.60.3](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.60.2...v3.60.3) (2024-12-10)


### Bug Fixes

* update to Next.js 15.1 ([a89c63e](https://github.com/ixartz/Next-js-Boilerplate/commit/a89c63eb26f6cb6fb4bff3bd01cf8628926386f9))

## [3.60.2](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.60.1...v3.60.2) (2024-12-05)


### Bug Fixes

* upgrade to react 19 stable version ([0f744e3](https://github.com/ixartz/Next-js-Boilerplate/commit/0f744e3f5525399da9adbf659889f2cf51f9bb4d))

## [3.60.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.60.0...v3.60.1) (2024-11-26)


### Bug Fixes

* update Arcjet integration and improve security checks ([516b75e](https://github.com/ixartz/Next-js-Boilerplate/commit/516b75e964f902f078ae4dc44f488796735769fb))

# [3.60.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.59.2...v3.60.0) (2024-11-26)


### Bug Fixes

* change Arcjet URL ([ac670a5](https://github.com/ixartz/Next-js-Boilerplate/commit/ac670a54735be9955404e9e3fc716aed39fdedc1))


### Features

* implement Arcjet security bot detection & Shield WAF ([96d95f4](https://github.com/ixartz/Next-js-Boilerplate/commit/96d95f45aff0df60ad9b6f1d64dd9b58098ea9ab))

## [3.59.2](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.59.1...v3.59.2) (2024-11-21)


### Bug Fixes

* switch back to en/default not loading ([cf7688a](https://github.com/ixartz/Next-js-Boilerplate/commit/cf7688ac4e19f9efa9e78508811bb06c785a9790))

## [3.59.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.59.0...v3.59.1) (2024-11-20)


### Bug Fixes

* set default lang for global error page from i18n routing ([e148bea](https://github.com/ixartz/Next-js-Boilerplate/commit/e148bea9d6290df220a73110f878132e4f818137))
* static rendering for sign in and sign up page ([2b6e75f](https://github.com/ixartz/Next-js-Boilerplate/commit/2b6e75faa0f2d04119c1ea107f09671773cab37f))

# [3.59.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.58.2...v3.59.0) (2024-11-15)


### Bug Fixes

* add await for header function and support only lang en for global error ([9ec5886](https://github.com/ixartz/Next-js-Boilerplate/commit/9ec588657191c0eb187e909e21f739043f9f8761))
* add legacy-peer-deps to support next.js 15 and react 19 ([bb9c803](https://github.com/ixartz/Next-js-Boilerplate/commit/bb9c80363383e109c71eeab30e2ba7011294924e))
* add onRequestError in instrumentations file to capture nested rsc errors ([4f144d0](https://github.com/ixartz/Next-js-Boilerplate/commit/4f144d03aba073e879a30a477c563cb6576bc3b4))
* add sponsor component in dashboard ([c172a5e](https://github.com/ixartz/Next-js-Boilerplate/commit/c172a5ef6c1ec731d297346d05f6ee4589127826))
* create a routing variables used by next-intl ([2ced1d8](https://github.com/ixartz/Next-js-Boilerplate/commit/2ced1d8f8467686a02e34124094f887210b4139a))
* enable react component annotation in Sentry ([87fa2fb](https://github.com/ixartz/Next-js-Boilerplate/commit/87fa2fb338130adc9ce0c1d017837c3c1d2c3d25))
* migrate clerk codebase to next.js 15 with await ([bb098ac](https://github.com/ixartz/Next-js-Boilerplate/commit/bb098acb68b4337112446c94959d3d2aed5aed89))
* migrate the codebase to use await params ([886a19e](https://github.com/ixartz/Next-js-Boilerplate/commit/886a19ee7141211a9c6c33c256246a7be73d3065))
* remove storybook until it support Next.js 15 ([2505167](https://github.com/ixartz/Next-js-Boilerplate/commit/25051670ca264feee02e186b23b9538c7a434cc1))
* remove storybook until it support Next.js 15 ([9fe8001](https://github.com/ixartz/Next-js-Boilerplate/commit/9fe800195ba889c5e0cd343f2a3799efd9f1b9c1))
* stop using jiti and use typescript for next.js config ([34a74fe](https://github.com/ixartz/Next-js-Boilerplate/commit/34a74fe20e30924b1536365a649d89e5fa476537))
* update CI workflow to use Node.js 22.x instead of 22.6 ([4215f37](https://github.com/ixartz/Next-js-Boilerplate/commit/4215f3705393a0fc5e503295c3e469d01fa8de2c))
* update image to respect default image ratio ([45689d2](https://github.com/ixartz/Next-js-Boilerplate/commit/45689d28459a389dc4b47bab133f28bda8f02702))
* update middleware matcher and use routermatcher for signup and sign in page ([96c42ae](https://github.com/ixartz/Next-js-Boilerplate/commit/96c42ae8c935ee6d3f3ca765196cca90e65dd614))
* use react 19 release candidate ([9387d49](https://github.com/ixartz/Next-js-Boilerplate/commit/9387d4963014b36d008b1d2efbcec196e0d78d17))


### Features

* update to next.js 15 along other npm dependencies ([bbcece3](https://github.com/ixartz/Next-js-Boilerplate/commit/bbcece334c44c314df4cb7736ac30d29b523d463))


### Reverts

* add back BaseTemplate storbyook file ([9884038](https://github.com/ixartz/Next-js-Boilerplate/commit/988403883c0bacd9074e2671a3765166742a6dd2))
* add back Storybook ([229ba7e](https://github.com/ixartz/Next-js-Boilerplate/commit/229ba7e6941e9415e64e6957d1a3163d0fd26082))

## [3.58.2](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.58.1...v3.58.2) (2024-10-17)


### Bug Fixes

* remove hydration error generated by Sentry Spotlight.js ([8b14146](https://github.com/ixartz/Next-js-Boilerplate/commit/8b14146bf6fe0eb288c2bcb8b701ca38b5dcec24))

## [3.58.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.58.0...v3.58.1) (2024-10-01)


### Bug Fixes

* simply the counter Route handler by removing try-catch block and improve readability in form ([f777914](https://github.com/ixartz/Next-js-Boilerplate/commit/f7779146e954a86c311577c24e2e80a846c36d46))
* use faker instead of Math.random, simply playwright config ([52c5d3f](https://github.com/ixartz/Next-js-Boilerplate/commit/52c5d3f3447ea26b730a284d2770bbf065caa058))

# [3.58.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.57.0...v3.58.0) (2024-09-15)


### Features

* add drizzle into the singleton only in dev to persist db connection ([aefe3d8](https://github.com/ixartz/Next-js-Boilerplate/commit/aefe3d838ede52a606869631257aa21a3e88a299))

# [3.57.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.56.2...v3.57.0) (2024-09-14)


### Features

* disable submit button when submitting form ([0247937](https://github.com/ixartz/Next-js-Boilerplate/commit/02479379326ff3b5b16f016de3241efcc5a49fd5))

## [3.56.2](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.56.1...v3.56.2) (2024-09-12)


### Bug Fixes

* rewrite index page of the boilerplate ([9456ede](https://github.com/ixartz/Next-js-Boilerplate/commit/9456ede93122e2230f6cd17114ac9893578e242d))

## [3.56.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.56.0...v3.56.1) (2024-09-11)


### Bug Fixes

* enable coverage for unit testing ([eb4534c](https://github.com/ixartz/Next-js-Boilerplate/commit/eb4534c1a30dd90943a35c1583ff9c0c60c9520d))
* update crowdin image, load the image locally ([5b404b1](https://github.com/ixartz/Next-js-Boilerplate/commit/5b404b1a1b451b6ea839f4a72e002efb8a8b0cf8))
* use default reporter for vitest ([67e766a](https://github.com/ixartz/Next-js-Boilerplate/commit/67e766a346a6f4eb06b146b6102b3751fc3e22ad))

# [3.56.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.55.3...v3.56.0) (2024-09-09)


### Bug Fixes

* add missing file ([54c343b](https://github.com/ixartz/Next-js-Boilerplate/commit/54c343b93a0555b60c1e7f000002f57c480694e6))


### Features

* use counter instead of guestbook, make the boilerplate easier to use ([3e6b0b8](https://github.com/ixartz/Next-js-Boilerplate/commit/3e6b0b86dfa8b0ef65c71830036021faa74180c5))

## [3.55.3](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.55.2...v3.55.3) (2024-08-21)


### Bug Fixes

* update checkly configuration and use playwright baseURl in Checkly ([4a458f3](https://github.com/ixartz/Next-js-Boilerplate/commit/4a458f328f100f427007ec9bd5c7a02e45c55a12))

## [3.55.2](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.55.1...v3.55.2) (2024-08-20)


### Bug Fixes

* add crowdin synchronize in CI action ([9402e5e](https://github.com/ixartz/Next-js-Boilerplate/commit/9402e5ee4c3e2b8dcb42fe38425b5e93247347c3))
* automatically sync translation with Crowdin for each PR ([d4ea7d5](https://github.com/ixartz/Next-js-Boilerplate/commit/d4ea7d5ed3e06f468c0cbd57a343c2925672fa1f))
* automatically sync translation with Crowdin for each PR ([eeb4216](https://github.com/ixartz/Next-js-Boilerplate/commit/eeb421668bcd0dc3fe09c8e9dea8c194e02207e2))
* checkout HEAD commit instead of the merge commit ([60bfd65](https://github.com/ixartz/Next-js-Boilerplate/commit/60bfd65d83e6e148c47703f4a94e11e1858d70c2))
* git checkout history in crowdin pr sync action ([5a2fedf](https://github.com/ixartz/Next-js-Boilerplate/commit/5a2fedf951f11c5d4e911e1b9c94dd1018d78df6))
* only run crowdin pr sync when CI workflow pass ([cc4a9a8](https://github.com/ixartz/Next-js-Boilerplate/commit/cc4a9a80c5c7ffa42bc0ad4a843f037f249ca423))
* upgrade GitHub actions dependencies ([7b5edfe](https://github.com/ixartz/Next-js-Boilerplate/commit/7b5edfe8ad7fad28e66a8ce92d63d2dd3e9dfbbe))

## [3.55.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.55.0...v3.55.1) (2024-08-20)


### Bug Fixes

* resolve lint sort conflicts ([b9d3de1](https://github.com/ixartz/Next-js-Boilerplate/commit/b9d3de1212b6b77cd61277edce354c290fc3fd86))
* resolve lint sort conflicts with simple-import-sort ([544c541](https://github.com/ixartz/Next-js-Boilerplate/commit/544c54197ee74930c01b0d28cfd250c6bc384080))

# [3.55.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.54.0...v3.55.0) (2024-08-19)


### Features

* remove target='blank' in badge ([b033c2e](https://github.com/ixartz/Next-js-Boilerplate/commit/b033c2e1f4ebdff7914ea81830e1c54b5b1a3d96))
* use new ESLint flat config ([8f31536](https://github.com/ixartz/Next-js-Boilerplate/commit/8f31536f29ce6599fb9ccbfae8dc176080a9215d))


### Reverts

* add back lint-staged file configuration ([5c51a94](https://github.com/ixartz/Next-js-Boilerplate/commit/5c51a94147c325ec2a10ea1c7a8f2060397ec32b))
* comment pre-commit script ([e9d6c7a](https://github.com/ixartz/Next-js-Boilerplate/commit/e9d6c7a34c1dad2b1fb706c300646f098b6437b1))
* pre-commit script used by Husky ([21acf33](https://github.com/ixartz/Next-js-Boilerplate/commit/21acf33b96dab666587688ea1dfb08c7f5f1ddf4))
* use default code from next-intl in global.d.ts ([0343b2f](https://github.com/ixartz/Next-js-Boilerplate/commit/0343b2f043480716866350b221a7e986759e6dc6))

# [3.54.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.53.0...v3.54.0) (2024-07-23)


### Features

* add NODE_ENV in t3 env ([17c23f9](https://github.com/ixartz/Next-js-Boilerplate/commit/17c23f9bea037da9ab2ae93b9ecc883a919d9723))
* add Sentry configuration in environment files and fix meta journal error in production ([2243510](https://github.com/ixartz/Next-js-Boilerplate/commit/2243510438d8b4e0670a309605852c817a6d8492))
* enable static rendering with i18n ([e6ec268](https://github.com/ixartz/Next-js-Boilerplate/commit/e6ec2682de7d8a5f1b92be67f1fa1499f800f624))
* middlware should not run for monitoring endpoint ([34b3c0c](https://github.com/ixartz/Next-js-Boilerplate/commit/34b3c0cb2cd732f937755e950197f03c765bdd15))
* use defineConfig in drizzle.config.ts ([48893e5](https://github.com/ixartz/Next-js-Boilerplate/commit/48893e535bb4889dd83c97aa809a6081b1e9afbd))

# [3.53.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.52.0...v3.53.0) (2024-06-26)


### Features

* add updateAt and createdAt attribute in guestbook ([80d369a](https://github.com/ixartz/Next-js-Boilerplate/commit/80d369a9d374cb5557356d9ea794719e3a1f59d5))
* create a new environement file for production ([988a051](https://github.com/ixartz/Next-js-Boilerplate/commit/988a051515666e7698a42f066198e7eb8dd44f32))
* switch to Postgres in Drizzle ORM ([1d725e8](https://github.com/ixartz/Next-js-Boilerplate/commit/1d725e8d280e1848e792aba7c8102371b3c038a8))

# [3.52.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.51.0...v3.52.0) (2024-05-31)


### Features

* update Drizzle configuration for Drizzle Kit 0.22 and improve ([5159455](https://github.com/ixartz/Next-js-Boilerplate/commit/5159455ab2cfb569702b33a7e2135ec23f32d598))

# [3.51.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.50.1...v3.51.0) (2024-05-29)


### Features

* update to Drizzle kit 0.21 version, no need to have dialect in command line ([62aa678](https://github.com/ixartz/Next-js-Boilerplate/commit/62aa6786117637e6b76c97f6c98f7ca6e8c343b0))

## [3.50.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.50.0...v3.50.1) (2024-05-20)


### Bug Fixes

* add eslint support for .mts file ([cd58d38](https://github.com/ixartz/Next-js-Boilerplate/commit/cd58d3806206e269d712e0976f4101af26275e44))

# [3.50.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.49.0...v3.50.0) (2024-05-18)


### Features

* replace Jest by Vitest for better DX ([2504504](https://github.com/ixartz/Next-js-Boilerplate/commit/25045041bb0af1fc4065ccffdb4d4d9b715c5823))
* update to Storybook v8 ([51b20a6](https://github.com/ixartz/Next-js-Boilerplate/commit/51b20a64f8f7a9780cb4c81b6ec2f0d1ac8779c5))


### Reverts

* reuse vitest.config.mts to avoid warning when running the tests ([f923242](https://github.com/ixartz/Next-js-Boilerplate/commit/f9232425d3cca895bcf3b45355dbee2caaedccce))

# [3.49.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.48.0...v3.49.0) (2024-05-17)


### Features

* vscode jest open test result view on test fails and add unauthenticatedUrl in clerk middleware ([2a68124](https://github.com/ixartz/Next-js-Boilerplate/commit/2a681244f834b6ea55bcd5cd3105f8b4a9df4a05))

# [3.48.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.47.0...v3.48.0) (2024-05-09)


### Features

* add custom configuration for i18n ally VSCode extension ([46f9459](https://github.com/ixartz/Next-js-Boilerplate/commit/46f945963c02eb29efc802fb0f3b1220b10bdf13))

# [3.47.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.46.0...v3.47.0) (2024-05-07)


### Features

* make dashboard without lang protected route in Clerk ([704466b](https://github.com/ixartz/Next-js-Boilerplate/commit/704466bbab40e366d0c1e17b66d7f5f0e97b902b))
* run Clerk middleware only needed ([5aeee06](https://github.com/ixartz/Next-js-Boilerplate/commit/5aeee0609bb9abbccf17aa0d2900cffdc7c3a18a))
* upgrade to Clerk v5 and use Clerk's Core 2 ([c1978f1](https://github.com/ixartz/Next-js-Boilerplate/commit/c1978f181a7c29e443fe407d91dfb9c2ae147f04))


### Reverts

* add back process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL ([f8cb9f4](https://github.com/ixartz/Next-js-Boilerplate/commit/f8cb9f441e08ec4f0e4501e4b42b4923adbc01a1))
* downgrade React to 18.2 due to testing errors, error raised in Next.js issue [#65161](https://github.com/ixartz/Next-js-Boilerplate/issues/65161) ([1815eb3](https://github.com/ixartz/Next-js-Boilerplate/commit/1815eb3670f53b4d949a06505e8ef3afd4ab0ee5))

# [3.46.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.45.0...v3.46.0) (2024-04-13)


### Features

* new turso logo ([3e781fc](https://github.com/ixartz/Next-js-Boilerplate/commit/3e781fc75201a7271a3a640a0b665adb1560add6))
* use new Turso tagline ([601ba6b](https://github.com/ixartz/Next-js-Boilerplate/commit/601ba6b2a4beb1a0c6779964d2d654bd3553f044))

# [3.45.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.44.1...v3.45.0) (2024-04-04)


### Features

* remove next-sitemap and use the native Next.js sitemap/robots.txt ([135a435](https://github.com/ixartz/Next-js-Boilerplate/commit/135a4350bef905d2a38a8901d42e5fa304fb92bc))

## [3.44.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.44.0...v3.44.1) (2024-04-03)


### Bug Fixes

* add Twitter in the index page ([75dfb8b](https://github.com/ixartz/Next-js-Boilerplate/commit/75dfb8bc5ca40446005f8d405add52d09071f62a))
* use new VSCode Jest configuration ([e92e4e0](https://github.com/ixartz/Next-js-Boilerplate/commit/e92e4e09c636944d85cec38683738520224acebb))

# [3.44.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.43.0...v3.44.0) (2024-04-02)


### Features

* run migration only in development and eslint-disable need to be at the top ([db94f31](https://github.com/ixartz/Next-js-Boilerplate/commit/db94f31615cd5ffcc3739ab56572646f7ce1f177))

# [3.43.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.42.0...v3.43.0) (2024-03-07)


### Features

* use eslintrc.json and give release.yml permission in GitHub Actions ([a329518](https://github.com/ixartz/Next-js-Boilerplate/commit/a32951811e157696ab915eebd6b71b09f49ccb83))

# [3.42.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.41.0...v3.42.0) (2024-02-22)


### Features

* remove import React when it's not needed ([a7082d3](https://github.com/ixartz/Next-js-Boilerplate/commit/a7082d3492d9a426218829f86554b2aeda9da8fd))

# [3.41.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.40.0...v3.41.0) (2024-02-09)


### Features

* add target blank for links going outside ([37ba36e](https://github.com/ixartz/Next-js-Boilerplate/commit/37ba36e5e3815d87cf882dc9aaf8b69b5849b49e))
* make the index page of the boilerplate cleaner ([f3a3f9b](https://github.com/ixartz/Next-js-Boilerplate/commit/f3a3f9b306bfaed85058d59cd15e62db158468ca))

# [3.40.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.39.0...v3.40.0) (2024-02-07)


### Features

* add pino.js as Logger ([1d35f43](https://github.com/ixartz/Next-js-Boilerplate/commit/1d35f43efd5e250498d2d30654be672e4e2d91c9))

# [3.39.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.38.0...v3.39.0) (2024-02-07)


### Features

* add preferType on VSCode ([a55bc6a](https://github.com/ixartz/Next-js-Boilerplate/commit/a55bc6a4b543c47ec491c5a84806f62c93dc1aa4))

# [3.38.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.37.0...v3.38.0) (2024-01-19)


### Features

* update to Next.js 14.1 ([5dab52d](https://github.com/ixartz/Next-js-Boilerplate/commit/5dab52d58648a12b5779f04d642ad4b2010931b0))

# [3.37.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.36.0...v3.37.0) (2024-01-13)


### Features

* add environment variables for one click deploy Netlify ([5becdbf](https://github.com/ixartz/Next-js-Boilerplate/commit/5becdbf59f43fdfe893c5b7b62cac1246787a07e))

# [3.36.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.35.0...v3.36.0) (2024-01-10)


### Features

* prod environement use the same method to migrate ([f6cfe7f](https://github.com/ixartz/Next-js-Boilerplate/commit/f6cfe7fa7583621c9161aa478f1d958d5c93c083))


### Reverts

* add back process.env.NODE_ENV check in README file for migrate ([853f3dc](https://github.com/ixartz/Next-js-Boilerplate/commit/853f3dc4cbade618902b382023fe6a6a8e947082))
* only run migration in development, if it run in production, it also run during the build ([c94a600](https://github.com/ixartz/Next-js-Boilerplate/commit/c94a6007b20f71fe10b10c76a05659364ee920ff))

# [3.35.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.34.0...v3.35.0) (2024-01-07)


### Features

* automatically run migrate in DB instead of running in NPM scripts ([b202686](https://github.com/ixartz/Next-js-Boilerplate/commit/b202686687a41eb38cf92a0451f03b5f0a854a2d))
* e2e tests run against next start with production code ([a57f724](https://github.com/ixartz/Next-js-Boilerplate/commit/a57f72402c459b75aec65472db7030557974643b))
* jest fail on console error and warn ([2dd92f2](https://github.com/ixartz/Next-js-Boilerplate/commit/2dd92f2db19df25210f0aa6eb8b9c44136a16ab7))


### Reverts

* change related to running playwright with next start ([1a2d0b6](https://github.com/ixartz/Next-js-Boilerplate/commit/1a2d0b6473e6e7b4965c7df353d39645a8688273))
* change related to running playwright with next start ([e9e0c17](https://github.com/ixartz/Next-js-Boilerplate/commit/e9e0c1790a8e76b51ee8a0b1012cc3492349bd1b))

# [3.34.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.33.0...v3.34.0) (2024-01-06)


### Features

* add type definition in Postcss config ([07906ff](https://github.com/ixartz/Next-js-Boilerplate/commit/07906ff20a7c8d2b0c24cc1f33c93b0bc541b9c3))
* change commitlint config from JS to TS ([6509805](https://github.com/ixartz/Next-js-Boilerplate/commit/650980539eb16c4ef0f5d1ed3e833cdb08faaf86))
* change jest config extension from js to TypeScript ([1cdea44](https://github.com/ixartz/Next-js-Boilerplate/commit/1cdea44c2a193e9df792dc997f6aa5304e043ff6))
* change nodeResolution to the new bundler from TypeScript 5.0 ([59282a2](https://github.com/ixartz/Next-js-Boilerplate/commit/59282a2f028a10b841f4af42248e4ecd2c41c080))
* convert Tailwind config file from JS to TS ([aff3b27](https://github.com/ixartz/Next-js-Boilerplate/commit/aff3b276c6b857570c3ec0b68de3cd5efaaeebbd))

# [3.33.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.32.1...v3.33.0) (2024-01-03)


### Features

* enable SWC compiler in Storybook ([5b4c61e](https://github.com/ixartz/Next-js-Boilerplate/commit/5b4c61ea11164b6e5853cefe363d2d433cda374d))

## [3.32.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.32.0...v3.32.1) (2023-12-27)


### Bug Fixes

* typo in en.json file for Portfolio word ([4d42b3d](https://github.com/ixartz/Next-js-Boilerplate/commit/4d42b3d11feeb1134961c0c688a6659b5e88364e))

# [3.32.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.31.0...v3.32.0) (2023-12-22)


### Features

* add code coverage reporting with Codecov ([08abd23](https://github.com/ixartz/Next-js-Boilerplate/commit/08abd23acbb5fb770046900901a367d60f18695e))

# [3.31.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.30.1...v3.31.0) (2023-12-20)


### Features

* add FIXME tag for Sentry configuration ([2eceef1](https://github.com/ixartz/Next-js-Boilerplate/commit/2eceef14257232c89f625acfe475c1aa7f220e46))
* add Sentry and launch spotlight.js in dev mode ([a1326ae](https://github.com/ixartz/Next-js-Boilerplate/commit/a1326aebb4ade33dc8a4429e749fb482ed906754))
* add spotlight ([34086c1](https://github.com/ixartz/Next-js-Boilerplate/commit/34086c1b8636bdc391c31ceed062a1e858d81539))
* enable Sentry Spotlight only in development mode ([62cc01a](https://github.com/ixartz/Next-js-Boilerplate/commit/62cc01ab2e1ae5594a4b91f931f313a904ff4b7d))
* ignore technical exception throw by React RSC in Sentry ([4bf9503](https://github.com/ixartz/Next-js-Boilerplate/commit/4bf95038600a28ea3e98e84dabec4df5fd9af609))
* in global error get locale in params and set in html lang attribute ([c3b4d25](https://github.com/ixartz/Next-js-Boilerplate/commit/c3b4d25d3be6a5ceed48f2d365bd14e44ff9b114))

## [3.30.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.30.0...v3.30.1) (2023-12-17)


### Bug Fixes

* api routes not found after apply intl middleware ([4650a5e](https://github.com/ixartz/Next-js-Boilerplate/commit/4650a5e293716dee7704c6082839aaf94b63e7ad)), closes [#209](https://github.com/ixartz/Next-js-Boilerplate/issues/209)

# [3.30.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.29.0...v3.30.0) (2023-12-12)


### Features

* add GitHub Actions to sync with Crowdin ([ccc86e9](https://github.com/ixartz/Next-js-Boilerplate/commit/ccc86e9e4df89dadd3214ae167972038f44108a6))

# [3.29.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.28.0...v3.29.0) (2023-12-08)


### Features

* add i18n support for client component and typesafety for i18n keys ([2d86247](https://github.com/ixartz/Next-js-Boilerplate/commit/2d862478414c4e6cf06e287acbef50369ef9a119))
* add i18n support for Dashboard url used in Clerk ([12b89bc](https://github.com/ixartz/Next-js-Boilerplate/commit/12b89bcfa1cae76872fc1504960a5ee417ef5eea))
* add i18n with Clerk components and remove custom style in global.css file ([5e1af6c](https://github.com/ixartz/Next-js-Boilerplate/commit/5e1af6c9a83cc6988c68fd761bf4945a2e0cdb9c))
* add i18n with next-intl ([1f43eb2](https://github.com/ixartz/Next-js-Boilerplate/commit/1f43eb247ad8591fef3aa8a34d112dd804eec4c3))
* add locale switcher UI to change lang ([13b40e3](https://github.com/ixartz/Next-js-Boilerplate/commit/13b40e32d265d341da1cf723c1af36f3ea53e7e1))
* add metatags in App Router for page migrated from Pages Router ([ce8c277](https://github.com/ixartz/Next-js-Boilerplate/commit/ce8c2770c41abcc3c866d7320de6ef4d8a541715))
* add support i18n for authMiddleware ([8651d36](https://github.com/ixartz/Next-js-Boilerplate/commit/8651d36279512b0f5e008341916110a8ee6f167a))
* add tests for page in App Router ([6a722a1](https://github.com/ixartz/Next-js-Boilerplate/commit/6a722a1fec7a236973f794edc6583a245ebb4747))
* convert all hard coded text and translate in french ([0c3b1b2](https://github.com/ixartz/Next-js-Boilerplate/commit/0c3b1b2f9a8ae5c0d34cb6f3a227a907aca00342))
* i18n for page metatag ([5e7676d](https://github.com/ixartz/Next-js-Boilerplate/commit/5e7676de0d58238de1d46e662c3c8e6e00bd2c5b))
* link in BaseTemplate replaced margin with gap ([28b6ff2](https://github.com/ixartz/Next-js-Boilerplate/commit/28b6ff24577b5d4338a7da068e06070c7f50f195))
* migreate the index page from Page Rotuer to App Router ([fd3e82c](https://github.com/ixartz/Next-js-Boilerplate/commit/fd3e82c2ff837951277a8300fd95f15294b9290a))
* move messages folder to locales ([305e385](https://github.com/ixartz/Next-js-Boilerplate/commit/305e38504939008ecfbbd3bfb6deaf052e57eae7))
* remove Page router and migrate about page to App Router ([3965cbf](https://github.com/ixartz/Next-js-Boilerplate/commit/3965cbf89a67a64272b895809a31791ccf383b57))
* translate text in dashboard layout ([8119f1d](https://github.com/ixartz/Next-js-Boilerplate/commit/8119f1db63853f83710a6cc1f3135b45bc209809))


### Reverts

* add back NEXT_PUBLIC_CLERK_SIGN_IN_URL in the previous location ([16ae2ef](https://github.com/ixartz/Next-js-Boilerplate/commit/16ae2ef3a7b2800a3ac4d847bb7afa70743ee805))
* add back style for a tag link ([c12a7bd](https://github.com/ixartz/Next-js-Boilerplate/commit/c12a7bd400c875a115eefe2a9921db9e36bf644d))
* use percy/cli 1.27.4 instead of 1.27.5, impossible to upload snapshort with 1.27.5 ([73f8a0b](https://github.com/ixartz/Next-js-Boilerplate/commit/73f8a0b0e9c69f83e5c5a2b51f52159fcc43c654))

# [3.28.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.27.0...v3.28.0) (2023-11-22)


### Features

* rename custom SignOutButton to LogOutButton to avoid confusion with Clerk SignOutButton ([183301b](https://github.com/ixartz/Next-js-Boilerplate/commit/183301b5e87bfa4479727c295e83b45b923454a0))
* use custom SignOutButton to apply custom CSS styles, unified with other nav links ([35094bf](https://github.com/ixartz/Next-js-Boilerplate/commit/35094bf038f0eae6e7e2d77238840c97cc7adabe))

# [3.27.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.26.0...v3.27.0) (2023-11-20)


### Features

* add PRODUCTION_URL environment variable and throw error when targetURL doesn't exist ([8134dee](https://github.com/ixartz/Next-js-Boilerplate/commit/8134dee84205e297020851bad4c81cf3906e7dfe))
* unified e2e tests for Checkly and playwright ([afa53f5](https://github.com/ixartz/Next-js-Boilerplate/commit/afa53f56b51f9a537131ceb046f90ea59c17dd71))
* use target URl instead of baseURL for checkly ([4fd61ed](https://github.com/ixartz/Next-js-Boilerplate/commit/4fd61edc77e1ef0d457cb829a89545f7dab47210))

# [3.26.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.25.0...v3.26.0) (2023-11-15)


### Features

* add a new GitHub Actions file for Checkly ([2109b1c](https://github.com/ixartz/Next-js-Boilerplate/commit/2109b1c75359a9ce89c2c0773fd65e78e1439403))
* add aria-label to fix jsx-a11y/control-has-associated-label error ([47e4ff4](https://github.com/ixartz/Next-js-Boilerplate/commit/47e4ff4f811b4e2071b9ba31f5c0ad1367b0caba))
* add email alert channel for checkly ([d1a4380](https://github.com/ixartz/Next-js-Boilerplate/commit/d1a43801d64fa261bdb252cf83dc289742f37294))
* add email channel in Checkly configuration to send emails when failing ([2019591](https://github.com/ixartz/Next-js-Boilerplate/commit/20195919d8a07f4e3cc0b7884e7d972de2935a94))
* create checkly config with a random working test ([32255b0](https://github.com/ixartz/Next-js-Boilerplate/commit/32255b0770ec5be84e9fd3321154329c556aedee))
* remove eslint rule customization in VSCode and use min(1) instead of nonempty (deprecated) ([9982a2d](https://github.com/ixartz/Next-js-Boilerplate/commit/9982a2d94fe7854eefaa754e9f41cf4735a81c86))
* update package-lock.json to fix CI ([1fff7ef](https://github.com/ixartz/Next-js-Boilerplate/commit/1fff7efe7295a9ee750b9f05af1a670db7bda733))

# [3.25.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.24.0...v3.25.0) (2023-10-30)


### Features

* release a new version for Next.js 14 and update README file ([4be2485](https://github.com/ixartz/Next-js-Boilerplate/commit/4be24850b75b9ca896e9e5546b8357745b128398))

# [3.24.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.23.0...v3.24.0) (2023-10-24)


### Features

* make guestbook endpoint avaiable to signed out users ([10b4d81](https://github.com/ixartz/Next-js-Boilerplate/commit/10b4d814d477e3475569537b1ef01a86b68c9a43))

# [3.23.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.22.0...v3.23.0) (2023-10-12)


### Features

* add playwright extension in VSCode ([956d1a8](https://github.com/ixartz/Next-js-Boilerplate/commit/956d1a8ec70c6a1214c72a115f0378507aa1b436))
* add playwright plugin in ESLint ([b2486f1](https://github.com/ixartz/Next-js-Boilerplate/commit/b2486f1b1090c458115b873ddc5bffa8ecaf8412))
* add Playwright: config, first test and dependency ([f054ea2](https://github.com/ixartz/Next-js-Boilerplate/commit/f054ea264bab3376ab7f86b0a0fdc1b6a4e98350))
* remove all Cypress related files and configurations ([9fe8271](https://github.com/ixartz/Next-js-Boilerplate/commit/9fe8271e667b819910702803f5489e99766fe9ff))


### Reverts

* the failing test in Navigation spec ([28996f5](https://github.com/ixartz/Next-js-Boilerplate/commit/28996f59d2f02562761609348000d55776365f7e))

# [3.22.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.21.0...v3.22.0) (2023-10-02)


### Features

* remove basePath in Next.js configuration ([7f9a0e6](https://github.com/ixartz/Next-js-Boilerplate/commit/7f9a0e6ed42aec7d9ec500531b7f519dc11a5ec9))
* remove no-img-element and use Next.js built-in <Image component ([383e3a3](https://github.com/ixartz/Next-js-Boilerplate/commit/383e3a38b98d92d59184275864888e9693a1cff7))

# [3.21.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.20.0...v3.21.0) (2023-09-25)


### Features

* update next.js to version 13.5 ([aa43f14](https://github.com/ixartz/Next-js-Boilerplate/commit/aa43f14bea16fcb4fd786d9fe74ae37bf29b5b5f))
* update storybook to the latest version and install playwright ([2079a34](https://github.com/ixartz/Next-js-Boilerplate/commit/2079a347bbbd08d2ffbc4ea96995eaaf66602373))

# [3.20.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.19.0...v3.20.0) (2023-09-01)


### Features

* make updatedAt working when the user update a message and rename the attribute to updatedAt ([4032bc0](https://github.com/ixartz/Next-js-Boilerplate/commit/4032bc0123660c20a72aa52ed611ea1f150e54af))

# [3.19.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.18.0...v3.19.0) (2023-08-30)


### Features

* make it easier to try edge runtime in the app router ([3f5fd58](https://github.com/ixartz/Next-js-Boilerplate/commit/3f5fd58d0980fdd35860d31d29b8f18e9c93b53f))

# [3.18.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.17.0...v3.18.0) (2023-08-27)


### Features

* remove MIGRATE_DB which not needed anymore with process.env.NODE_ENV ([3fe81ae](https://github.com/ixartz/Next-js-Boilerplate/commit/3fe81ae98440b33ce18cee80265fdaa54e242184))

# [3.17.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.16.0...v3.17.0) (2023-08-27)


### Features

* add schema in drizzle instance and disable migrate in production ([5e26798](https://github.com/ixartz/Next-js-Boilerplate/commit/5e2679877a3da64a4cabfc22fdaacebd6abe6789))
* add script to migrate before building next.js ([220d05e](https://github.com/ixartz/Next-js-Boilerplate/commit/220d05e5d028852ccc533ca60b187bc3d47c5d73))
* do not run db migration when building on GitHub actions ([964cfa1](https://github.com/ixartz/Next-js-Boilerplate/commit/964cfa1a02fb41b387c851f0b2293c673859d60a))
* reload guestbook page when deployed on production ([c2e91b2](https://github.com/ixartz/Next-js-Boilerplate/commit/c2e91b2df944b0659d1768d2a7cc54a494d7d5c1))
* replace dotenv/config by dotenv-cli in db:studio NPM scripts ([f7f8743](https://github.com/ixartz/Next-js-Boilerplate/commit/f7f87435a984fa9d0407a7602d1ef38563c5e8d0))

# [3.16.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.15.0...v3.16.0) (2023-08-24)


### Bug Fixes

* build issues with prerendering ([ff117b9](https://github.com/ixartz/Next-js-Boilerplate/commit/ff117b9750e3609cebbf53a5dea01f0fbf94f865))


### Features

* add .env file for production ([58ed68c](https://github.com/ixartz/Next-js-Boilerplate/commit/58ed68cc2eefb1274e6e268c40a3ed8cd7d936be))
* add authToken support for production Turso ([26b8276](https://github.com/ixartz/Next-js-Boilerplate/commit/26b827618199f1dd73453c7ec021c13a4aaf5f7b))
* add await for migrate function ([96793f0](https://github.com/ixartz/Next-js-Boilerplate/commit/96793f0adedb10f802dfb46ff96b85f14c78ebf3))
* add database powered by Turso in guestbook page ([64073a5](https://github.com/ixartz/Next-js-Boilerplate/commit/64073a5babb38327a23fd3ae2b354152306e7977))
* add db file in gitignore ([cd45e09](https://github.com/ixartz/Next-js-Boilerplate/commit/cd45e0906cc79e87302ee6b88674089c5de059a3))
* add drizzle config and database schema ([df30388](https://github.com/ixartz/Next-js-Boilerplate/commit/df30388002ead9121ffb764e1bd11a71550cbe06))
* add style for guestbook ([339154c](https://github.com/ixartz/Next-js-Boilerplate/commit/339154ccfdaf7e53aeefd12fe0e347c645be5163))
* add typesafe environment variables ([5a2cd78](https://github.com/ixartz/Next-js-Boilerplate/commit/5a2cd78aca2fc60e6c0d4861ff656e7ba2ac86c4))
* create guestbook should not accept empty username and email ([37e4408](https://github.com/ixartz/Next-js-Boilerplate/commit/37e4408f968b36332a0a8ae9a90c687eee7fb4a0))
* implement AddGuestbookForm to create new guestbook message ([d7b37e6](https://github.com/ixartz/Next-js-Boilerplate/commit/d7b37e63f65d528e599b14d64cbf3ac5b2d3feba))
* implement delete guestbook entry ([b7f823a](https://github.com/ixartz/Next-js-Boilerplate/commit/b7f823a83435856ac32aea90da8317926e5b2b8b))
* improve UI for AddGuestbookForm ([153abfc](https://github.com/ixartz/Next-js-Boilerplate/commit/153abfc0e2f10a5aa59e24af8f0ef76667041578))
* insert in guestbook and retrieve all guestbooks ([23ee408](https://github.com/ixartz/Next-js-Boilerplate/commit/23ee4086a8c2166bdd6fe82b1cb50cc286793bb3))
* make guestbook editable ([8ec1406](https://github.com/ixartz/Next-js-Boilerplate/commit/8ec14066a966c76b02bf5552ec2f4f348048a45c))
* remove notnull in schema.ts ([10f4943](https://github.com/ixartz/Next-js-Boilerplate/commit/10f49434999ba0a884a72e640c67dc955bf7eedd))
* rename from email to username ([52ab0e4](https://github.com/ixartz/Next-js-Boilerplate/commit/52ab0e4f86b20ace52cbb6ce421f85357c0dfa6e))
* replace new-router page by guestbook ([efc84e6](https://github.com/ixartz/Next-js-Boilerplate/commit/efc84e607d23981dba07b931ff078776aa9693b5))
* replace with a working URL for the database to avoid timeout ([fecd8a5](https://github.com/ixartz/Next-js-Boilerplate/commit/fecd8a5d66934af774fde12759f8079cabfb382b))
* update dotenv path to .env, the file was renamed ([bd9b2c9](https://github.com/ixartz/Next-js-Boilerplate/commit/bd9b2c9efd12a0b54125ac352c43aab9d31c7c99))
* use local SQLite file ([fe52801](https://github.com/ixartz/Next-js-Boilerplate/commit/fe528010cf2d867fcbbc53156ae7fa6c862a88f4))
* validate t3 env on build ([6d448ed](https://github.com/ixartz/Next-js-Boilerplate/commit/6d448ed0fdea51952c8bfeaf4ce948cf9365675c))

# [3.15.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.14.1...v3.15.0) (2023-08-10)


### Features

* add next.js middleware with Clerk ([2f4a1d3](https://github.com/ixartz/Next-js-Boilerplate/commit/2f4a1d3e394eb835b011a13289f156a91993d782))
* add sign in and sign up link in index page ([4489085](https://github.com/ixartz/Next-js-Boilerplate/commit/4489085e8deb0ae1836a3741657f8331af6294ca))
* add sign in and sign up page ([f021f71](https://github.com/ixartz/Next-js-Boilerplate/commit/f021f71f755e3af3cb789d0330ad2a0237ec600d))
* add sign out button in dashboard ([c663d1c](https://github.com/ixartz/Next-js-Boilerplate/commit/c663d1c4799869faf2a2c549669521409f192830))
* add user profile to manage account ([470731b](https://github.com/ixartz/Next-js-Boilerplate/commit/470731ba960dfdd0aa57f66affde28b0226d5d42))
* add user profile to manage account ([581efbe](https://github.com/ixartz/Next-js-Boilerplate/commit/581efbef51cf700f9bbe94f268ff99639f5e49da))
* implement hello component by display user email address ([7047985](https://github.com/ixartz/Next-js-Boilerplate/commit/7047985ffbce9a986e7308040928783395cf7b68))
* implement sign out button ([8588834](https://github.com/ixartz/Next-js-Boilerplate/commit/8588834b5f1a53c51835d7aba5a4c9f1230c1bf7))
* implement sign out button and redirect to sign in page when logging out ([45ed137](https://github.com/ixartz/Next-js-Boilerplate/commit/45ed137d5c4e292ac8329f0661cb38fc29812927))
* redirect to dashboard when the user is signed in for sign up and sign in page ([629a033](https://github.com/ixartz/Next-js-Boilerplate/commit/629a03363af310e5411fea4cb554b53e72701e7d))

## [3.14.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.14.0...v3.14.1) (2023-08-07)


### Bug Fixes

* resolve sourcemap error with Cypress and TypeScript 5 ([54a5100](https://github.com/ixartz/Next-js-Boilerplate/commit/54a51004d6e22860eb1c6aad4ff689fac46bd0b4))

# [3.14.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.13.0...v3.14.0) (2023-08-03)


### Features

* use Next.js custom TypeScript plugin ([915e193](https://github.com/ixartz/Next-js-Boilerplate/commit/915e193f8037d36e9779fe7464a4d6c1685b3a94))

# [3.13.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.12.0...v3.13.0) (2023-08-02)


### Features

* add app routed pages ([9cc79a0](https://github.com/ixartz/Next-js-Boilerplate/commit/9cc79a00647b0a4ce64f66da4a430ec2c4972367)), closes [#64](https://github.com/ixartz/Next-js-Boilerplate/issues/64)
* add sitemap support app router ([b82e566](https://github.com/ixartz/Next-js-Boilerplate/commit/b82e566fb43d63329ef4507870494e554dea0e6a))
* app router doesn't support next export, use output: export ([76aa9cd](https://github.com/ixartz/Next-js-Boilerplate/commit/76aa9cd0597ad06fd0f0160ad6119a25b87d3336))
* generate statically portfolio pages ([1f1bf31](https://github.com/ixartz/Next-js-Boilerplate/commit/1f1bf3143215ab19d19cd4f13e4048b0ee84073c))
* update test for new router page ([b695666](https://github.com/ixartz/Next-js-Boilerplate/commit/b695666fd41c9ddf1886e9b5e3c7cc43b616820c))

# [3.12.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.11.0...v3.12.0) (2023-07-13)


### Features

* format code to respect prettier ([48b6a49](https://github.com/ixartz/Next-js-Boilerplate/commit/48b6a49fd204083deb94b01aab70b52a42b9593f))
* resolve conflict between airbnb-hook and next/core-web-vitals about react hooks ([5e0be4f](https://github.com/ixartz/Next-js-Boilerplate/commit/5e0be4fd8c2f9acd895f0b9ce373af7d782d44df))
* update to the latest dependencies version ([d93fd83](https://github.com/ixartz/Next-js-Boilerplate/commit/d93fd83b6ab93360ddd8489afc8cfb05603e504c))


### Reverts

* use older TypeScript to avoid e2e compilation with sourcemap ([6377d2f](https://github.com/ixartz/Next-js-Boilerplate/commit/6377d2f2efc71384fba236427086b4e75f189328))

# [3.11.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.10.1...v3.11.0) (2023-06-07)


### Features

* update dependencies to the latest version ([b7609de](https://github.com/ixartz/Next-js-Boilerplate/commit/b7609dea1c8bd49f6ac05439740ea78894cd4a79))

## [3.10.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.10.0...v3.10.1) (2023-05-29)


### Bug Fixes

* added types ([b35ddc9](https://github.com/ixartz/Next-js-Boilerplate/commit/b35ddc91ecad81986432dce1ba84c302e6394a5b))

# [3.10.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.9.0...v3.10.0) (2023-04-26)


### Features

* add vscode yoavbls.pretty-ts-errors extension ([3588ce1](https://github.com/ixartz/Next-js-Boilerplate/commit/3588ce1dd366ebaa69f97551be58528d1ae38457))
* remove stories in the coverage from Jest ([d502869](https://github.com/ixartz/Next-js-Boilerplate/commit/d502869a08a0b1d9025a4ce582651c5353f29d59))
* use default airbnb instead of the base version ([5c05116](https://github.com/ixartz/Next-js-Boilerplate/commit/5c05116fb777aee09c1af7df6694e54403eaaccb))

# [3.9.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.8.2...v3.9.0) (2023-04-05)


### Features

* add storybook into project ([51f3748](https://github.com/ixartz/Next-js-Boilerplate/commit/51f3748c0cb6d9cd04cdb0d3b9d95a0f60851866))
* add tailwind css support in Storybook ([5e0d287](https://github.com/ixartz/Next-js-Boilerplate/commit/5e0d287cef8a898df8f1a98632a8703657282100))
* remove warning for no extreneous deps in stories ([b243d44](https://github.com/ixartz/Next-js-Boilerplate/commit/b243d441e4b75566e16f5fa64d26900267eb89f5))


### Reverts

* remove storybook addon-styling which is not needed ([e863fed](https://github.com/ixartz/Next-js-Boilerplate/commit/e863fedcbc5a1aaf808c295d80f8de95b6abd1f7))

## [3.8.2](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.8.1...v3.8.2) (2023-03-28)


### Bug Fixes

* error generated by eslint-plugin-cypress ([7562c6b](https://github.com/ixartz/Next-js-Boilerplate/commit/7562c6bddb31e6941aee7e4e2bbcdabf5be3bddf))

## [3.8.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.8.0...v3.8.1) (2023-03-16)


### Bug Fixes

* typo in Readme ([8f7c1b7](https://github.com/ixartz/Next-js-Boilerplate/commit/8f7c1b79a46406b04b90ed8a5fe5029b3c24ff8c))

# [3.8.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.7.0...v3.8.0) (2023-03-02)


### Features

* fix heading levels increase by one ([e712e60](https://github.com/ixartz/Next-js-Boilerplate/commit/e712e60402f04033673d93e464d7b3c46fff7dbe))

# [3.7.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.6.0...v3.7.0) (2023-02-05)


### Features

* improve accessibility ([aa0f0b1](https://github.com/ixartz/Next-js-Boilerplate/commit/aa0f0b12085e31f13574fc9f4349157102d4467b))


### Reverts

* add support for all Node.js 14+, too restrictive with only Node.js 18+ ([4e27540](https://github.com/ixartz/Next-js-Boilerplate/commit/4e27540f638d4767fb60b612519669ad6bf69367))
* downgrade semantic-release version to 19 ([26d5a6e](https://github.com/ixartz/Next-js-Boilerplate/commit/26d5a6ebe2fc4fe59fef40779e132ccf1f31c09f))

# [3.6.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.5.4...v3.6.0) (2022-12-03)


### Bug Fixes

* add npx before percy command line ([4824e98](https://github.com/ixartz/Next-js-Boilerplate/commit/4824e98a4d621684494fe2c7e8c3351551e52845))
* retrive PERCY_TOKEN and set token for percy cli ([afe00f2](https://github.com/ixartz/Next-js-Boilerplate/commit/afe00f2e47b5dbc5fb701dd2e46756f4b7e498fd))
* wait until the link rendered instead a wrong heading tag ([e38655b](https://github.com/ixartz/Next-js-Boilerplate/commit/e38655b853b39fdcb9bccd3a84e99dd5caa1681d))


### Features

* add visual testing with Percy ([b0a39f5](https://github.com/ixartz/Next-js-Boilerplate/commit/b0a39f58e1bd0934158b0bab8ab7e4c9215e88f0))

## [3.5.4](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.5.3...v3.5.4) (2022-12-03)


### Bug Fixes

* change matching regex for Cypress files ([861d545](https://github.com/ixartz/Next-js-Boilerplate/commit/861d54596b61b7706cfbb681df334d73b34a378e))

## [3.5.3](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.5.2...v3.5.3) (2022-12-02)


### Bug Fixes

* resolve merge conflict ([276f57a](https://github.com/ixartz/Next-js-Boilerplate/commit/276f57aeb0d4a346f8e19ad81ce4703458d9f41c))

## [3.5.2](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.5.1...v3.5.2) (2022-12-02)


### Bug Fixes

* use npx npm-check-updates ([e530193](https://github.com/ixartz/Next-js-Boilerplate/commit/e5301939a5ff98c598899ff49bee1ad351759292))

## [3.5.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.5.0...v3.5.1) (2022-12-02)


### Bug Fixes

* add steps in update-deps.yml file, syntax error ([b5de445](https://github.com/ixartz/Next-js-Boilerplate/commit/b5de445f1f927a5a7c2b0c85746b8fd07629cb55))

# [3.5.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.4.0...v3.5.0) (2022-12-02)


### Features

* add auto-update GitHub Actions ([364168f](https://github.com/ixartz/Next-js-Boilerplate/commit/364168f3407c7cdd21da7cd1de6d9d930f89d99a))

# [3.4.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.3.0...v3.4.0) (2022-12-02)


### Features

* automatically format the whole codebase with npm run format ([9299209](https://github.com/ixartz/Next-js-Boilerplate/commit/92992096ede4d2b3e77c3e0c96b75e5e6b84067d))
* update footer message and comment ([4f74176](https://github.com/ixartz/Next-js-Boilerplate/commit/4f74176b05528666fd8b92a8becdc7e3c2f0db4a))

# [3.3.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.2.4...v3.3.0) (2022-11-22)


### Features

* change 'powered by' text to 'built' with ([fe0a29f](https://github.com/ixartz/Next-js-Boilerplate/commit/fe0a29f8fbab14c7e8c8e98a75ce488ac157e509))

## [3.2.4](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.2.3...v3.2.4) (2022-11-20)


### Bug Fixes

* update README file for next-sitemap ([9496217](https://github.com/ixartz/Next-js-Boilerplate/commit/94962171a35a07e84319374500f28a76f264a266))

## [3.2.3](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.2.2...v3.2.3) (2022-11-20)


### Bug Fixes

* add sitemap file in gitignore, it shouldn't commit to git ([344b731](https://github.com/ixartz/Next-js-Boilerplate/commit/344b7312df2f7e12e642a6346ef05ad9a7ca766c))

## [3.2.2](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.2.1...v3.2.2) (2022-11-20)


### Bug Fixes

* rename from mjs to js next-sitemap file ([7d450ff](https://github.com/ixartz/Next-js-Boilerplate/commit/7d450ffce77f0be4c533cb1aab757f7fb1f13596))

## [3.2.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.2.0...v3.2.1) (2022-11-20)


### Bug Fixes

* code styling in blog component pages ([f4a55c4](https://github.com/ixartz/Next-js-Boilerplate/commit/f4a55c4234fc03ed719859c12f13bffabd120c6d))
* move getStaticPaths at the top of blog page ([83892ea](https://github.com/ixartz/Next-js-Boilerplate/commit/83892ea865459f59da824c9358fbf4ccea6475e6))
* remove generated files by next-sitemap ([c5d93bf](https://github.com/ixartz/Next-js-Boilerplate/commit/c5d93bf9fe67a6737b536edf4d50d56cd4c8af2c))

# [3.2.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.1.0...v3.2.0) (2022-11-19)


### Features

* run github release only on completed CI workflow ([dd4de76](https://github.com/ixartz/Next-js-Boilerplate/commit/dd4de76b6ea013190a6ea18d69eb3764e1b915f9))

# [3.1.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.0.0...v3.1.0) (2022-11-19)


### Bug Fixes

* just rebuild sitemap ([831bae9](https://github.com/ixartz/Next-js-Boilerplate/commit/831bae93831eb5c4f259c4a0fa9ec3012ede8927))


### Features

* add blog page ([89c4ec7](https://github.com/ixartz/Next-js-Boilerplate/commit/89c4ec79db48f4ae09af3e8ddb3ce5a980ed8ee6))
* add sitemap.xml and robots.txt from build ([545d133](https://github.com/ixartz/Next-js-Boilerplate/commit/545d133decee4f7d42c228049ef3bde2b9a94b0a))
* disable Husky for release ([f20c595](https://github.com/ixartz/Next-js-Boilerplate/commit/f20c5951e018c99421e833eef6ce14bd9632838f))
* rename from master to main ([10920ec](https://github.com/ixartz/Next-js-Boilerplate/commit/10920ece4892ca73639388116af59fdd3e077d5f))
* update TypeScript to 4.9.x ([471dc70](https://github.com/ixartz/Next-js-Boilerplate/commit/471dc70306c69ecb524af40aa76403daa83597e2))

# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [3.0.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v2.1.1...v3.0.0) (2022-10-26)


### ⚠ BREAKING CHANGES

* update to Next.js 13 and Tailwind CSS 3.2

### Features

* add commit script in package.json ([8f4719e](https://github.com/ixartz/Next-js-Boilerplate/commit/8f4719ec550ab0dbffa93ca1d278aa9e370a773a))


### Bug Fixes

* Eslint comment update ([8baa5d1](https://github.com/ixartz/Next-js-Boilerplate/commit/8baa5d160734a3cadb419534509cc6edaac57456))


* update to Next.js 13 and Tailwind CSS 3.2 ([fc9f2c1](https://github.com/ixartz/Next-js-Boilerplate/commit/fc9f2c1cf914c15b36cdf881306d20b405a259e8))

### [2.1.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v2.1.0...v2.1.1) (2022-09-08)

## [2.1.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v2.0.0...v2.1.0) (2022-07-08)


### Features

* add cypress and cypress eslint plugin ([5657ee6](https://github.com/ixartz/Next-js-Boilerplate/commit/5657ee6dab03b11020bb2ce80083669785edd6ce))

## [2.0.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v1.1.0...v2.0.0) (2022-07-03)


### ⚠ BREAKING CHANGES

* add Jest and React testing library
* to React 18

### Features

* add coverage for vscode-jest and configure jest autoRun ([ad8a030](https://github.com/ixartz/Next-js-Boilerplate/commit/ad8a03019010577bfb8e8ed850e8d45ca274dbe9))
* add Jest and React testing library ([e182b87](https://github.com/ixartz/Next-js-Boilerplate/commit/e182b87db5943abbe706568e77285e1eb6bddf5e))
* add TypeScript support for Tailwind CSS configuration ([41f1918](https://github.com/ixartz/Next-js-Boilerplate/commit/41f19189655abe3941485363e057812a5fcd6c02))
* add vscode jest extension ([49ab935](https://github.com/ixartz/Next-js-Boilerplate/commit/49ab935a03f5a9d1074a155331107737fd7dad13))


* to React 18 ([c78f215](https://github.com/ixartz/Next-js-Boilerplate/commit/c78f2152a978a39b2c6d381427df8e8ad2a30099))

## 1.1.0 (2022-04-25)


### Features

* add commitlint with config-conventional ([97a9ac7](https://github.com/ixartz/Next-js-Boilerplate/commit/97a9ac7dbbca3f8d4fad22a9e4a481c029cd2cb5))


### Bug Fixes

* add missing files for commitzen ([018ba8b](https://github.com/ixartz/Next-js-Boilerplate/commit/018ba8bde81b0f6cc60230fe4668b149ac3b2e6a))
* update package-lock.json ([fba016d](https://github.com/ixartz/Next-js-Boilerplate/commit/fba016dec202d5748e30804b1bf50e30c00ef120))
