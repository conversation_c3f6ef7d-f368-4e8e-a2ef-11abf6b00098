# 🏗️ HVAC COSMIC PLATFORM - ARCHITECTURE OVERVIEW

## 🚀 **REACT + SUPABASE HVAC MANAGEMENT ECOSYSTEM**

### 🌟 **Technology Stack - Cosmic Power**

```typescript
// Core Technology Stack
interface HVACTechStack {
  frontend: {
    framework: 'React 18 + TypeScript';
    styling: 'Tailwind CSS + Framer Motion';
    stateManagement: 'Zustand + React Query';
    routing: 'React Router v6';
    forms: 'React Hook Form + Zod';
    charts: 'Recharts + D3.js';
    maps: 'Mapbox GL JS';
    realTime: 'Supabase Realtime';
  };
  backend: {
    database: 'Supabase PostgreSQL';
    auth: 'Supabase Auth';
    storage: 'Supabase Storage';
    realtime: 'Supabase Realtime';
    functions: 'Supabase Edge Functions';
    api: 'Supabase REST + GraphQL';
  };
  integrations: {
    email: 'Resend + React Email';
    sms: 'Twilio';
    transcription: 'OpenAI Whisper API';
    weather: 'OpenWeatherMap API';
    maps: 'Mapbox + Google Maps';
    payments: 'Stripe';
    calendar: 'Google Calendar API';
  };
}
```

### 🏠 **Database Schema - Supabase Tables**

```sql
-- Core HVAC Tables
CREATE TABLE companies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  address JSONB,
  phone TEXT,
  email TEXT,
  license_number TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE customers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES companies(id),
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  address JSONB,
  customer_type TEXT CHECK (customer_type IN ('residential', 'commercial')),
  preferred_contact TEXT CHECK (preferred_contact IN ('email', 'phone', 'sms')),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE buildings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID REFERENCES customers(id),
  name TEXT NOT NULL,
  address JSONB NOT NULL,
  building_type TEXT CHECK (building_type IN ('residential', 'commercial', 'industrial')),
  square_footage INTEGER,
  floors INTEGER,
  year_built INTEGER,
  hvac_systems JSONB,
  access_instructions TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE technicians (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES companies(id),
  user_id UUID REFERENCES auth.users(id),
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  license_number TEXT,
  specializations TEXT[],
  hourly_rate DECIMAL(10,2),
  vehicle_info JSONB,
  current_location POINT,
  status TEXT CHECK (status IN ('available', 'busy', 'off_duty', 'emergency')) DEFAULT 'available',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE service_tickets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES companies(id),
  customer_id UUID REFERENCES customers(id),
  building_id UUID REFERENCES buildings(id),
  assigned_technician_id UUID REFERENCES technicians(id),
  ticket_number TEXT UNIQUE NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  priority TEXT CHECK (priority IN ('low', 'normal', 'high', 'emergency')) DEFAULT 'normal',
  status TEXT CHECK (status IN ('open', 'assigned', 'in_progress', 'completed', 'cancelled')) DEFAULT 'open',
  service_type TEXT CHECK (service_type IN ('repair', 'maintenance', 'installation', 'inspection')),
  scheduled_date TIMESTAMP WITH TIME ZONE,
  completed_date TIMESTAMP WITH TIME ZONE,
  estimated_duration INTEGER, -- minutes
  actual_duration INTEGER, -- minutes
  parts_used JSONB,
  labor_cost DECIMAL(10,2),
  parts_cost DECIMAL(10,2),
  total_cost DECIMAL(10,2),
  customer_signature TEXT,
  photos TEXT[],
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE equipment (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  building_id UUID REFERENCES buildings(id),
  equipment_type TEXT NOT NULL, -- 'furnace', 'ac_unit', 'heat_pump', etc.
  brand TEXT,
  model TEXT,
  serial_number TEXT,
  installation_date DATE,
  warranty_expiration DATE,
  last_service_date DATE,
  next_service_date DATE,
  specifications JSONB,
  maintenance_schedule JSONB,
  status TEXT CHECK (status IN ('operational', 'needs_service', 'broken', 'replaced')) DEFAULT 'operational',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE invoices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES companies(id),
  customer_id UUID REFERENCES customers(id),
  service_ticket_id UUID REFERENCES service_tickets(id),
  invoice_number TEXT UNIQUE NOT NULL,
  issue_date DATE NOT NULL,
  due_date DATE NOT NULL,
  subtotal DECIMAL(10,2) NOT NULL,
  tax_amount DECIMAL(10,2) DEFAULT 0,
  total_amount DECIMAL(10,2) NOT NULL,
  status TEXT CHECK (status IN ('draft', 'sent', 'paid', 'overdue', 'cancelled')) DEFAULT 'draft',
  payment_date DATE,
  payment_method TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE communications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES companies(id),
  customer_id UUID REFERENCES customers(id),
  service_ticket_id UUID REFERENCES service_tickets(id),
  type TEXT CHECK (type IN ('email', 'sms', 'phone', 'in_person')) NOT NULL,
  direction TEXT CHECK (direction IN ('inbound', 'outbound')) NOT NULL,
  subject TEXT,
  content TEXT,
  attachments TEXT[],
  transcription TEXT,
  sentiment_score DECIMAL(3,2), -- -1 to 1
  read_status BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE parts_inventory (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES companies(id),
  part_number TEXT NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  category TEXT,
  brand TEXT,
  cost DECIMAL(10,2),
  price DECIMAL(10,2),
  quantity_on_hand INTEGER DEFAULT 0,
  minimum_stock INTEGER DEFAULT 0,
  supplier_info JSONB,
  location TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_service_tickets_status ON service_tickets(status);
CREATE INDEX idx_service_tickets_priority ON service_tickets(priority);
CREATE INDEX idx_service_tickets_scheduled_date ON service_tickets(scheduled_date);
CREATE INDEX idx_technicians_status ON technicians(status);
CREATE INDEX idx_technicians_location ON technicians USING GIST(current_location);
CREATE INDEX idx_communications_customer ON communications(customer_id);
CREATE INDEX idx_communications_created_at ON communications(created_at);
```

### 🔐 **Row Level Security (RLS) Policies**

```sql
-- Enable RLS on all tables
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE buildings ENABLE ROW LEVEL SECURITY;
ALTER TABLE technicians ENABLE ROW LEVEL SECURITY;
ALTER TABLE service_tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE equipment ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE communications ENABLE ROW LEVEL SECURITY;
ALTER TABLE parts_inventory ENABLE ROW LEVEL SECURITY;

-- Company-based access policies
CREATE POLICY "Users can access their company data" ON companies
  FOR ALL USING (
    id IN (
      SELECT company_id FROM technicians 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can access customers from their company" ON customers
  FOR ALL USING (
    company_id IN (
      SELECT company_id FROM technicians 
      WHERE user_id = auth.uid()
    )
  );

-- Similar policies for other tables...
```

### 🚀 **React Component Architecture**

```typescript
// Component Structure
src/
├── components/
│   ├── hvac/
│   │   ├── dashboard/
│   │   │   ├── HVACDashboard.tsx
│   │   │   ├── BuildingOverview.tsx
│   │   │   ├── TechnicianStatus.tsx
│   │   │   └── ServiceMetrics.tsx
│   │   ├── tickets/
│   │   │   ├── ServiceTicketList.tsx
│   │   │   ├── TicketForm.tsx
│   │   │   ├── TicketDetails.tsx
│   │   │   └── TicketPrioritizer.tsx
│   │   ├── technicians/
│   │   │   ├── TechnicianMap.tsx
│   │   │   ├── TechnicianSchedule.tsx
│   │   │   ├── MobileInterface.tsx
│   │   │   └── RouteOptimizer.tsx
│   │   ├── customers/
│   │   │   ├── CustomerList.tsx
│   │   │   ├── CustomerProfile.tsx
│   │   │   ├── CommunicationHub.tsx
│   │   │   └── CustomerPortal.tsx
│   │   ├── equipment/
│   │   │   ├── EquipmentTracker.tsx
│   │   │   ├── MaintenanceScheduler.tsx
│   │   │   ├── EquipmentHistory.tsx
│   │   │   └── PredictiveMaintenance.tsx
│   │   ├── financial/
│   │   │   ├── InvoiceManager.tsx
│   │   │   ├── ProfitabilityAnalysis.tsx
│   │   │   ├── CashFlowForecast.tsx
│   │   │   └── CostOptimizer.tsx
│   │   └── communications/
│   │       ├── EmailCenter.tsx
│   │       ├── SMSManager.tsx
│   │       ├── CallTranscription.tsx
│   │       └── SentimentAnalysis.tsx
│   ├── ui/
│   │   ├── cosmic/
│   │   │   ├── CosmicCard.tsx
│   │   │   ├── CosmicButton.tsx
│   │   │   ├── CosmicInput.tsx
│   │   │   ├── CosmicProgress.tsx
│   │   │   └── CosmicBadge.tsx
│   │   └── charts/
│   │       ├── TemperatureChart.tsx
│   │       ├── EnergyChart.tsx
│   │       ├── ProfitChart.tsx
│   │       └── PerformanceChart.tsx
│   └── layout/
│       ├── HVACLayout.tsx
│       ├── Sidebar.tsx
│       ├── Header.tsx
│       └── MobileNav.tsx
├── hooks/
│   ├── useSupabase.ts
│   ├── useRealtime.ts
│   ├── useGeolocation.ts
│   ├── useTranscription.ts
│   └── useWeather.ts
├── services/
│   ├── supabase.ts
│   ├── email.service.ts
│   ├── sms.service.ts
│   ├── transcription.service.ts
│   ├── weather.service.ts
│   └── maps.service.ts
├── types/
│   ├── hvac.types.ts
│   ├── database.types.ts
│   └── api.types.ts
└── utils/
    ├── hvac.utils.ts
    ├── date.utils.ts
    ├── currency.utils.ts
    └── validation.utils.ts
```

### 🌟 **Supabase Configuration**

```typescript
// src/services/supabase.ts
import { createClient } from '@supabase/supabase-js';
import { Database } from '../types/database.types';

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL!;
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY!;

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
});

// Real-time subscriptions for HVAC data
export const subscribeToServiceTickets = (companyId: string, callback: (payload: any) => void) => {
  return supabase
    .channel('service_tickets')
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'service_tickets',
        filter: `company_id=eq.${companyId}`
      },
      callback
    )
    .subscribe();
};

export const subscribeToTechnicianLocations = (companyId: string, callback: (payload: any) => void) => {
  return supabase
    .channel('technician_locations')
    .on(
      'postgres_changes',
      {
        event: 'UPDATE',
        schema: 'public',
        table: 'technicians',
        filter: `company_id=eq.${companyId}`
      },
      callback
    )
    .subscribe();
};
```

### 📱 **Mobile-First HVAC Interface**

```typescript
// src/components/hvac/technicians/MobileInterface.tsx
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useGeolocation } from '../../../hooks/useGeolocation';
import { useTranscription } from '../../../hooks/useTranscription';

interface MobileInterfaceProps {
  technicianId: string;
  currentTicket?: ServiceTicket;
}

const MobileInterface: React.FC<MobileInterfaceProps> = ({
  technicianId,
  currentTicket
}) => {
  const { location, updateLocation } = useGeolocation();
  const { startTranscription, stopTranscription, transcript } = useTranscription();
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // Voice commands for hands-free operation
  const handleVoiceCommand = (command: string) => {
    switch (command.toLowerCase()) {
      case 'start job':
        startJob();
        break;
      case 'complete task':
        completeCurrentTask();
        break;
      case 'request parts':
        openPartsRequest();
        break;
      case 'emergency':
        triggerEmergencyProtocol();
        break;
    }
  };

  return (
    <div className="mobile-hvac-interface min-h-screen bg-gray-50">
      {/* Status Bar */}
      <div className="bg-hvac-primary text-white p-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isOnline ? 'bg-green-400' : 'bg-red-400'}`} />
            <span className="text-sm">{isOnline ? 'Online' : 'Offline'}</span>
          </div>
          <div className="text-sm">
            Technician: {technicianId}
          </div>
        </div>
      </div>

      {/* Current Job Card */}
      {currentTicket && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="m-4 bg-white rounded-lg shadow-lg p-4"
        >
          <div className="flex justify-between items-start mb-3">
            <h2 className="text-lg font-semibold">{currentTicket.title}</h2>
            <span className={`px-2 py-1 rounded text-xs font-medium ${
              currentTicket.priority === 'emergency' ? 'bg-red-100 text-red-800' :
              currentTicket.priority === 'high' ? 'bg-orange-100 text-orange-800' :
              'bg-blue-100 text-blue-800'
            }`}>
              {currentTicket.priority}
            </span>
          </div>
          
          <div className="space-y-2 text-sm text-gray-600">
            <p>📍 {currentTicket.building.address}</p>
            <p>📞 {currentTicket.customer.phone}</p>
            <p>⏰ Scheduled: {new Date(currentTicket.scheduled_date).toLocaleString()}</p>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-2 gap-2 mt-4">
            <button className="hvac-button hvac-button--primary">
              Start Job
            </button>
            <button className="hvac-button hvac-button--outline">
              Call Customer
            </button>
            <button className="hvac-button hvac-button--outline">
              View Equipment
            </button>
            <button className="hvac-button hvac-button--outline">
              Add Photos
            </button>
          </div>
        </motion.div>
      )}

      {/* Voice Control */}
      <div className="fixed bottom-4 right-4">
        <button
          onTouchStart={() => startTranscription()}
          onTouchEnd={() => stopTranscription()}
          className="w-16 h-16 bg-hvac-primary text-white rounded-full shadow-lg flex items-center justify-center"
        >
          🎤
        </button>
      </div>

      {/* Offline Indicator */}
      {!isOnline && (
        <div className="fixed top-16 left-4 right-4 bg-yellow-100 border border-yellow-400 text-yellow-800 px-4 py-2 rounded">
          Working offline. Data will sync when connection is restored.
        </div>
      )}
    </div>
  );
};

export default MobileInterface;
```

### 🌟 **Real-Time Features Implementation**

```typescript
// src/hooks/useRealtime.ts
import { useEffect, useState } from 'react';
import { supabase } from '../services/supabase';

export const useRealtimeServiceTickets = (companyId: string) => {
  const [tickets, setTickets] = useState<ServiceTicket[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Initial fetch
    const fetchTickets = async () => {
      const { data, error } = await supabase
        .from('service_tickets')
        .select(`
          *,
          customer:customers(*),
          building:buildings(*),
          technician:technicians(*)
        `)
        .eq('company_id', companyId)
        .order('created_at', { ascending: false });

      if (data) {
        setTickets(data);
      }
      setLoading(false);
    };

    fetchTickets();

    // Real-time subscription
    const subscription = supabase
      .channel('service_tickets_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'service_tickets',
          filter: `company_id=eq.${companyId}`
        },
        (payload) => {
          if (payload.eventType === 'INSERT') {
            setTickets(prev => [payload.new as ServiceTicket, ...prev]);
          } else if (payload.eventType === 'UPDATE') {
            setTickets(prev => prev.map(ticket => 
              ticket.id === payload.new.id ? payload.new as ServiceTicket : ticket
            ));
          } else if (payload.eventType === 'DELETE') {
            setTickets(prev => prev.filter(ticket => ticket.id !== payload.old.id));
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [companyId]);

  return { tickets, loading };
};
```

This architecture provides the foundation for a complete HVAC management system with React and Supabase. The next files will detail specific functionalities like customer flow, email integration, transcription, and full wind power features.
