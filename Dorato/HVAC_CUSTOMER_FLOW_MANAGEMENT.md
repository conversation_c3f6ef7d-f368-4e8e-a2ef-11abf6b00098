# 🏠 HVAC CUSTOMER FLOW MANAGEMENT - PEŁNA MOC WIATRU

## 🚀 **COMPLETE CUSTOMER JOURNEY AUTOMATION WITH COSMIC POWER**

### 🌟 **Customer Lifecycle Management**

```typescript
// Customer Journey Stages
interface CustomerJourney {
  stage: 'lead' | 'prospect' | 'customer' | 'loyal' | 'advocate';
  touchpoints: TouchPoint[];
  automations: Automation[];
  metrics: JourneyMetrics;
}

interface TouchPoint {
  id: string;
  type: 'website' | 'phone' | 'email' | 'sms' | 'in_person' | 'social';
  timestamp: Date;
  content: string;
  sentiment: number; // -1 to 1
  response_required: boolean;
  automated_response?: string;
}

// Customer Flow States
enum CustomerFlowState {
  INITIAL_CONTACT = 'initial_contact',
  QUOTE_REQUESTED = 'quote_requested',
  QUOTE_SENT = 'quote_sent',
  QUOTE_APPROVED = 'quote_approved',
  SERVICE_SCHEDULED = 'service_scheduled',
  SERVICE_IN_PROGRESS = 'service_in_progress',
  SERVICE_COMPLETED = 'service_completed',
  INVOICE_SENT = 'invoice_sent',
  PAYMENT_RECEIVED = 'payment_received',
  FOLLOW_UP_SCHEDULED = 'follow_up_scheduled',
  MAINTENANCE_PROGRAM = 'maintenance_program'
}
```

### 🔄 **Automated Customer Flow Engine**

```typescript
// src/services/customerFlow.service.ts
import { supabase } from './supabase';
import { EmailService } from './email.service';
import { SMSService } from './sms.service';

export class CustomerFlowEngine {
  private emailService = new EmailService();
  private smsService = new SMSService();

  async processCustomerFlow(customerId: string, event: CustomerEvent) {
    const customer = await this.getCustomer(customerId);
    const currentState = customer.flow_state;
    
    switch (event.type) {
      case 'initial_contact':
        await this.handleInitialContact(customer, event);
        break;
      case 'quote_request':
        await this.handleQuoteRequest(customer, event);
        break;
      case 'service_completion':
        await this.handleServiceCompletion(customer, event);
        break;
      case 'payment_received':
        await this.handlePaymentReceived(customer, event);
        break;
    }
  }

  private async handleInitialContact(customer: Customer, event: CustomerEvent) {
    // 1. Send welcome message
    await this.sendWelcomeMessage(customer);
    
    // 2. Create initial service ticket
    const ticket = await this.createServiceTicket(customer, event);
    
    // 3. Schedule follow-up call
    await this.scheduleFollowUp(customer, '2 hours');
    
    // 4. Update customer state
    await this.updateCustomerState(customer.id, CustomerFlowState.INITIAL_CONTACT);
    
    // 5. Notify sales team
    await this.notifySalesTeam(customer, ticket);
  }

  private async handleQuoteRequest(customer: Customer, event: CustomerEvent) {
    // 1. Generate automated quote
    const quote = await this.generateQuote(customer, event.serviceDetails);
    
    // 2. Send quote via preferred channel
    if (customer.preferred_contact === 'email') {
      await this.emailService.sendQuote(customer.email, quote);
    } else {
      await this.smsService.sendQuoteLink(customer.phone, quote.id);
    }
    
    // 3. Schedule quote follow-up
    await this.scheduleFollowUp(customer, '24 hours');
    
    // 4. Update state
    await this.updateCustomerState(customer.id, CustomerFlowState.QUOTE_SENT);
  }

  private async handleServiceCompletion(customer: Customer, event: CustomerEvent) {
    const serviceTicket = event.serviceTicket;
    
    // 1. Send completion notification
    await this.sendServiceCompletionMessage(customer, serviceTicket);
    
    // 2. Generate and send invoice
    const invoice = await this.generateInvoice(serviceTicket);
    await this.emailService.sendInvoice(customer.email, invoice);
    
    // 3. Request feedback
    await this.requestFeedback(customer, serviceTicket);
    
    // 4. Schedule maintenance reminder
    await this.scheduleMaintenanceReminder(customer, serviceTicket);
    
    // 5. Update state
    await this.updateCustomerState(customer.id, CustomerFlowState.SERVICE_COMPLETED);
  }

  private async sendWelcomeMessage(customer: Customer) {
    const message = `
      Hi ${customer.first_name}! 👋
      
      Thank you for contacting ${process.env.COMPANY_NAME}! 
      We're your local HVAC experts with 20+ years of experience.
      
      🔧 Emergency repairs available 24/7
      ❄️ AC installation & maintenance  
      🔥 Heating system specialists
      💰 Free estimates on all services
      
      A team member will contact you within 2 hours.
      
      Need immediate assistance? Call: ${process.env.EMERGENCY_PHONE}
      
      Best regards,
      The ${process.env.COMPANY_NAME} Team
    `;

    if (customer.preferred_contact === 'email') {
      await this.emailService.sendWelcomeEmail(customer.email, message);
    } else {
      await this.smsService.sendWelcomeSMS(customer.phone, message);
    }
  }

  private async generateQuote(customer: Customer, serviceDetails: any) {
    // AI-powered quote generation based on:
    // - Service type
    // - Building size
    // - Equipment age
    // - Historical data
    // - Market rates
    
    const basePrice = this.calculateBasePrice(serviceDetails);
    const adjustments = this.calculateAdjustments(customer, serviceDetails);
    const finalPrice = basePrice + adjustments;

    return {
      id: `quote-${Date.now()}`,
      customer_id: customer.id,
      service_type: serviceDetails.type,
      description: serviceDetails.description,
      line_items: this.generateLineItems(serviceDetails),
      subtotal: finalPrice,
      tax: finalPrice * 0.08, // 8% tax
      total: finalPrice * 1.08,
      valid_until: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      terms: this.getQuoteTerms(),
      created_at: new Date()
    };
  }
}
```

### 📧 **Email Integration with React Email**

```typescript
// src/services/email.service.ts
import { Resend } from 'resend';
import { render } from '@react-email/render';
import { WelcomeEmail } from '../emails/WelcomeEmail';
import { QuoteEmail } from '../emails/QuoteEmail';
import { InvoiceEmail } from '../emails/InvoiceEmail';
import { ServiceReminderEmail } from '../emails/ServiceReminderEmail';

export class EmailService {
  private resend = new Resend(process.env.RESEND_API_KEY);

  async sendWelcomeEmail(to: string, customerData: any) {
    const html = render(WelcomeEmail({ 
      customerName: customerData.first_name,
      companyName: process.env.COMPANY_NAME,
      emergencyPhone: process.env.EMERGENCY_PHONE
    }));

    return await this.resend.emails.send({
      from: `${process.env.COMPANY_NAME} <hello@${process.env.DOMAIN}>`,
      to,
      subject: `Welcome to ${process.env.COMPANY_NAME} - Your HVAC Experts!`,
      html,
      tags: [
        { name: 'category', value: 'welcome' },
        { name: 'customer_type', value: 'new' }
      ]
    });
  }

  async sendQuote(to: string, quote: Quote) {
    const html = render(QuoteEmail({ 
      quote,
      companyInfo: {
        name: process.env.COMPANY_NAME,
        phone: process.env.COMPANY_PHONE,
        email: process.env.COMPANY_EMAIL,
        address: process.env.COMPANY_ADDRESS
      }
    }));

    return await this.resend.emails.send({
      from: `${process.env.COMPANY_NAME} <quotes@${process.env.DOMAIN}>`,
      to,
      subject: `Your HVAC Service Quote #${quote.id}`,
      html,
      attachments: [
        {
          filename: `quote-${quote.id}.pdf`,
          content: await this.generateQuotePDF(quote)
        }
      ],
      tags: [
        { name: 'category', value: 'quote' },
        { name: 'quote_id', value: quote.id }
      ]
    });
  }

  async sendServiceReminder(to: string, appointment: Appointment) {
    const html = render(ServiceReminderEmail({ 
      appointment,
      customerName: appointment.customer.first_name,
      technicianName: appointment.technician.first_name,
      companyName: process.env.COMPANY_NAME
    }));

    return await this.resend.emails.send({
      from: `${process.env.COMPANY_NAME} <service@${process.env.DOMAIN}>`,
      to,
      subject: `Service Reminder: Tomorrow at ${appointment.scheduled_time}`,
      html,
      tags: [
        { name: 'category', value: 'reminder' },
        { name: 'appointment_id', value: appointment.id }
      ]
    });
  }

  async sendInvoice(to: string, invoice: Invoice) {
    const html = render(InvoiceEmail({ 
      invoice,
      paymentLink: `${process.env.APP_URL}/pay/${invoice.id}`,
      companyInfo: {
        name: process.env.COMPANY_NAME,
        phone: process.env.COMPANY_PHONE,
        email: process.env.COMPANY_EMAIL
      }
    }));

    return await this.resend.emails.send({
      from: `${process.env.COMPANY_NAME} <billing@${process.env.DOMAIN}>`,
      to,
      subject: `Invoice #${invoice.invoice_number} - ${process.env.COMPANY_NAME}`,
      html,
      attachments: [
        {
          filename: `invoice-${invoice.invoice_number}.pdf`,
          content: await this.generateInvoicePDF(invoice)
        }
      ],
      tags: [
        { name: 'category', value: 'invoice' },
        { name: 'invoice_id', value: invoice.id }
      ]
    });
  }
}
```

### 📱 **SMS Integration with Twilio**

```typescript
// src/services/sms.service.ts
import twilio from 'twilio';

export class SMSService {
  private client = twilio(
    process.env.TWILIO_ACCOUNT_SID,
    process.env.TWILIO_AUTH_TOKEN
  );

  async sendWelcomeSMS(to: string, customerName: string) {
    const message = `Hi ${customerName}! Welcome to ${process.env.COMPANY_NAME}. We'll contact you within 2 hours. Emergency? Call ${process.env.EMERGENCY_PHONE}`;

    return await this.client.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to,
      statusCallback: `${process.env.API_URL}/webhooks/sms-status`
    });
  }

  async sendQuoteLink(to: string, quoteId: string) {
    const quoteUrl = `${process.env.APP_URL}/quote/${quoteId}`;
    const message = `Your HVAC quote is ready! View and approve: ${quoteUrl}. Questions? Reply to this message.`;

    return await this.client.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to
    });
  }

  async sendServiceReminder(to: string, appointment: Appointment) {
    const message = `Reminder: ${process.env.COMPANY_NAME} service tomorrow at ${appointment.scheduled_time}. Technician: ${appointment.technician.first_name}. Need to reschedule? Reply RESCHEDULE`;

    return await this.client.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to
    });
  }

  async sendTechnicianETA(to: string, technicianName: string, eta: string) {
    const message = `${technicianName} is on the way! ETA: ${eta}. Track live: ${process.env.APP_URL}/track/${appointment.id}`;

    return await this.client.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to
    });
  }

  async sendPaymentReminder(to: string, invoice: Invoice) {
    const paymentUrl = `${process.env.APP_URL}/pay/${invoice.id}`;
    const message = `Payment reminder: Invoice #${invoice.invoice_number} ($${invoice.total_amount}) is due. Pay online: ${paymentUrl}`;

    return await this.client.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to
    });
  }

  // Handle incoming SMS responses
  async handleIncomingSMS(from: string, body: string) {
    const customer = await this.findCustomerByPhone(from);
    
    if (!customer) {
      // New lead via SMS
      await this.createLeadFromSMS(from, body);
      return;
    }

    // Process customer response
    const intent = this.analyzeIntent(body);
    
    switch (intent) {
      case 'reschedule':
        await this.handleRescheduleRequest(customer, body);
        break;
      case 'emergency':
        await this.handleEmergencyRequest(customer, body);
        break;
      case 'question':
        await this.handleCustomerQuestion(customer, body);
        break;
      default:
        await this.forwardToHuman(customer, body);
    }
  }

  private analyzeIntent(message: string): string {
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('reschedule') || lowerMessage.includes('change')) {
      return 'reschedule';
    }
    if (lowerMessage.includes('emergency') || lowerMessage.includes('urgent')) {
      return 'emergency';
    }
    if (lowerMessage.includes('?') || lowerMessage.includes('question')) {
      return 'question';
    }
    
    return 'general';
  }
}
```

### 🎯 **Customer Segmentation & Targeting**

```typescript
// src/services/customerSegmentation.service.ts
export class CustomerSegmentationService {
  
  async segmentCustomers(companyId: string) {
    const customers = await this.getAllCustomers(companyId);
    
    return {
      newCustomers: this.filterNewCustomers(customers),
      loyalCustomers: this.filterLoyalCustomers(customers),
      atRiskCustomers: this.filterAtRiskCustomers(customers),
      highValueCustomers: this.filterHighValueCustomers(customers),
      maintenanceCustomers: this.filterMaintenanceCustomers(customers),
      emergencyCustomers: this.filterEmergencyCustomers(customers)
    };
  }

  private filterLoyalCustomers(customers: Customer[]): Customer[] {
    return customers.filter(customer => {
      const serviceCount = customer.service_tickets?.length || 0;
      const lastServiceDate = customer.last_service_date;
      const daysSinceLastService = lastServiceDate ? 
        (Date.now() - new Date(lastServiceDate).getTime()) / (1000 * 60 * 60 * 24) : 
        Infinity;
      
      return serviceCount >= 3 && daysSinceLastService < 365;
    });
  }

  private filterAtRiskCustomers(customers: Customer[]): Customer[] {
    return customers.filter(customer => {
      const lastServiceDate = customer.last_service_date;
      const daysSinceLastService = lastServiceDate ? 
        (Date.now() - new Date(lastServiceDate).getTime()) / (1000 * 60 * 60 * 24) : 
        Infinity;
      
      return daysSinceLastService > 730; // 2 years
    });
  }

  async createTargetedCampaign(segment: string, customers: Customer[]) {
    const campaigns = {
      newCustomers: {
        subject: "Welcome to the family! Special discount inside 🏠",
        template: "new_customer_welcome",
        discount: 0.15
      },
      loyalCustomers: {
        subject: "Thank you for your loyalty - Exclusive maintenance program",
        template: "loyalty_reward",
        offer: "maintenance_program"
      },
      atRiskCustomers: {
        subject: "We miss you! Come back with 25% off",
        template: "win_back",
        discount: 0.25
      },
      maintenanceCustomers: {
        subject: "Time for your seasonal HVAC check-up",
        template: "maintenance_reminder",
        cta: "schedule_maintenance"
      }
    };

    const campaign = campaigns[segment];
    if (!campaign) return;

    for (const customer of customers) {
      await this.sendTargetedMessage(customer, campaign);
    }
  }
}
```

### 📊 **Customer Analytics Dashboard**

```typescript
// src/components/hvac/analytics/CustomerAnalyticsDashboard.tsx
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  LineChart, Line, AreaChart, Area, BarChart, Bar, 
  XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer 
} from 'recharts';

interface CustomerMetrics {
  totalCustomers: number;
  newCustomersThisMonth: number;
  customerRetentionRate: number;
  averageCustomerValue: number;
  customerSatisfactionScore: number;
  churnRate: number;
}

const CustomerAnalyticsDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<CustomerMetrics>();
  const [customerGrowth, setCustomerGrowth] = useState([]);
  const [segmentData, setSegmentData] = useState([]);

  useEffect(() => {
    loadCustomerAnalytics();
  }, []);

  const loadCustomerAnalytics = async () => {
    // Load customer metrics from Supabase
    const { data: customers } = await supabase
      .from('customers')
      .select(`
        *,
        service_tickets(count),
        invoices(total_amount)
      `);

    const metrics = calculateCustomerMetrics(customers);
    setMetrics(metrics);
  };

  return (
    <div className="customer-analytics-dashboard p-6">
      <h1 className="text-3xl font-bold mb-6">Customer Analytics</h1>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="hvac-card text-center"
        >
          <div className="text-2xl font-bold text-hvac-primary">
            {metrics?.totalCustomers}
          </div>
          <div className="text-sm text-gray-600">Total Customers</div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="hvac-card text-center"
        >
          <div className="text-2xl font-bold text-hvac-cooling">
            {metrics?.newCustomersThisMonth}
          </div>
          <div className="text-sm text-gray-600">New This Month</div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="hvac-card text-center"
        >
          <div className="text-2xl font-bold text-hvac-heating">
            {metrics?.customerRetentionRate}%
          </div>
          <div className="text-sm text-gray-600">Retention Rate</div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="hvac-card text-center"
        >
          <div className="text-2xl font-bold text-hvac-neutral">
            ${metrics?.averageCustomerValue}
          </div>
          <div className="text-sm text-gray-600">Avg Customer Value</div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="hvac-card text-center"
        >
          <div className="text-2xl font-bold text-hvac-warning">
            {metrics?.customerSatisfactionScore}/5
          </div>
          <div className="text-sm text-gray-600">Satisfaction Score</div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="hvac-card text-center"
        >
          <div className="text-2xl font-bold text-hvac-critical">
            {metrics?.churnRate}%
          </div>
          <div className="text-sm text-gray-600">Churn Rate</div>
        </motion.div>
      </div>

      {/* Customer Growth Chart */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="hvac-card mb-8"
      >
        <h2 className="text-xl font-semibold mb-4">Customer Growth</h2>
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={customerGrowth}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Area 
              type="monotone" 
              dataKey="customers" 
              stroke="#4A90E2" 
              fill="#4A90E2" 
              fillOpacity={0.3} 
            />
          </AreaChart>
        </ResponsiveContainer>
      </motion.div>

      {/* Customer Segments */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
        className="hvac-card"
      >
        <h2 className="text-xl font-semibold mb-4">Customer Segments</h2>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={segmentData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="segment" />
            <YAxis />
            <Tooltip />
            <Bar dataKey="count" fill="#7ED321" />
          </BarChart>
        </ResponsiveContainer>
      </motion.div>
    </div>
  );
};

export default CustomerAnalyticsDashboard;
```

This comprehensive customer flow management system provides automated customer journey handling, multi-channel communication, intelligent segmentation, and detailed analytics - all with the cosmic power and wind force your HVAC business needs!
