import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  BeakerIcon,
  DocumentTextIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon,
  CpuChipIcon,
  SparklesIcon,
  EyeIcon,
  ShieldCheckIcon,
} from '@heroicons/react/24/outline';

interface GeneticVariant {
  id: string;
  gene: string;
  rsid: string;
  chromosome: string;
  position: number;
  genotype: string;
  riskAllele: string;
  frequency: number;
  impact: 'high' | 'moderate' | 'low' | 'protective';
  category: 'metabolism' | 'absorption' | 'sensitivity' | 'efficacy' | 'toxicity';
}

interface SupplementRecommendation {
  id: string;
  supplement: string;
  dosageModification: string;
  timingRecommendation: string;
  reasoning: string;
  confidence: number;
  supportingVariants: string[];
  contraindications: string[];
}

interface MetabolismProfile {
  cyp2d6: 'poor' | 'intermediate' | 'normal' | 'rapid' | 'ultrarapid';
  cyp3a4: 'poor' | 'intermediate' | 'normal' | 'rapid';
  mthfr: 'normal' | 'heterozygous' | 'homozygous';
  comt: 'met/met' | 'met/val' | 'val/val';
  apoe: 'e2/e2' | 'e2/e3' | 'e2/e4' | 'e3/e3' | 'e3/e4' | 'e4/e4';
}

interface GeneticReport {
  id: string;
  userId: string;
  uploadDate: Date;
  processingStatus: 'uploaded' | 'processing' | 'completed' | 'error';
  variantsAnalyzed: number;
  supplementRecommendations: SupplementRecommendation[];
  metabolismProfile: MetabolismProfile;
  riskFactors: string[];
  protectiveFactors: string[];
}

interface CosmicGeneticAnalyzerProps {
  userId: string;
  onReportGenerated?: (report: GeneticReport) => void;
}

const CosmicGeneticAnalyzer: React.FC<CosmicGeneticAnalyzerProps> = ({
  userId,
  onReportGenerated,
}) => {
  const [geneticReport, setGeneticReport] = useState<GeneticReport | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStage, setProcessingStage] = useState('');
  const [uploadProgress, setUploadProgress] = useState(0);
  const [selectedVariants, setSelectedVariants] = useState<GeneticVariant[]>([]);
  const [showSensitiveData, setShowSensitiveData] = useState(false);
  const [cosmicAccuracy, setCosmicAccuracy] = useState(99.7);

  useEffect(() => {
    // Cosmic accuracy fluctuation
    const accuracyInterval = setInterval(() => {
      setCosmicAccuracy(prev => {
        const fluctuation = (Math.random() - 0.5) * 0.2;
        return Math.max(99.5, Math.min(99.9, prev + fluctuation));
      });
    }, 3000);

    return () => clearInterval(accuracyInterval);
  }, []);

  const processGeneticData = useCallback(async () => {
    setIsProcessing(true);
    setUploadProgress(0);

    try {
      // Stage 1: Upload and validation
      setProcessingStage('Uploading genetic data...');
      await simulateProgress(20);

      // Stage 2: Quality control
      setProcessingStage('Performing quality control checks...');
      await simulateProgress(40);

      // Stage 3: Variant calling
      setProcessingStage('Identifying genetic variants...');
      await simulateProgress(60);

      // Stage 4: Annotation
      setProcessingStage('Annotating variants with pharmacogenomic data...');
      await simulateProgress(80);

      // Stage 5: Report generation
      setProcessingStage('Generating personalized supplement recommendations...');
      await simulateProgress(100);

      // Generate sample report
      const report = generateSampleReport();
      setGeneticReport(report);
      onReportGenerated?.(report);

    } catch (error) {
      console.error('Error processing genetic data:', error);
    } finally {
      setIsProcessing(false);
      setProcessingStage('');
      setUploadProgress(0);
    }
  }, [onReportGenerated]);

  const simulateProgress = async (targetProgress: number) => {
    const currentProgress = uploadProgress;
    const steps = 20;
    const increment = (targetProgress - currentProgress) / steps;

    for (let i = 0; i < steps; i++) {
      await new Promise(resolve => setTimeout(resolve, 100));
      setUploadProgress(prev => Math.min(targetProgress, prev + increment));
    }
  };

  const generateSampleReport = (): GeneticReport => {
    const sampleVariants: GeneticVariant[] = [
      {
        id: 'var-1',
        gene: 'CYP2D6',
        rsid: 'rs1065852',
        chromosome: '22',
        position: 42126611,
        genotype: 'G/A',
        riskAllele: 'A',
        frequency: 0.35,
        impact: 'high',
        category: 'metabolism',
      },
      {
        id: 'var-2',
        gene: 'MTHFR',
        rsid: 'rs1801133',
        chromosome: '1',
        position: 11796321,
        genotype: 'C/T',
        riskAllele: 'T',
        frequency: 0.32,
        impact: 'moderate',
        category: 'metabolism',
      },
      {
        id: 'var-3',
        gene: 'COMT',
        rsid: 'rs4680',
        chromosome: '22',
        position: 19963748,
        genotype: 'G/A',
        riskAllele: 'A',
        frequency: 0.48,
        impact: 'moderate',
        category: 'metabolism',
      },
    ];

    const sampleRecommendations: SupplementRecommendation[] = [
      {
        id: 'rec-1',
        supplement: 'Methylfolate (5-MTHF)',
        dosageModification: 'Increase to 800-1000 mcg daily',
        timingRecommendation: 'Take with breakfast',
        reasoning: 'MTHFR C677T variant reduces folate metabolism efficiency by 65%',
        confidence: 0.94,
        supportingVariants: ['rs1801133'],
        contraindications: [],
      },
      {
        id: 'rec-2',
        supplement: 'Vitamin B6 (P5P)',
        dosageModification: 'Standard dosage 25-50mg daily',
        timingRecommendation: 'Take with meals',
        reasoning: 'Normal B6 metabolism based on genetic profile',
        confidence: 0.87,
        supportingVariants: ['rs4654748'],
        contraindications: [],
      },
      {
        id: 'rec-3',
        supplement: 'Magnesium Glycinate',
        dosageModification: 'Reduce to 200mg daily',
        timingRecommendation: 'Take 2 hours before bed',
        reasoning: 'COMT Met/Val variant indicates slower dopamine clearance',
        confidence: 0.91,
        supportingVariants: ['rs4680'],
        contraindications: ['Avoid with kidney disease'],
      },
    ];

    return {
      id: `report-${Date.now()}`,
      userId,
      uploadDate: new Date(),
      processingStatus: 'completed',
      variantsAnalyzed: 847392,
      supplementRecommendations: sampleRecommendations,
      metabolismProfile: {
        cyp2d6: 'intermediate',
        cyp3a4: 'normal',
        mthfr: 'heterozygous',
        comt: 'met/val',
        apoe: 'e3/e3',
      },
      riskFactors: [
        'Reduced folate metabolism',
        'Intermediate drug metabolism',
        'Increased oxidative stress sensitivity',
      ],
      protectiveFactors: [
        'Normal vitamin D receptor function',
        'Enhanced antioxidant capacity',
        'Optimal omega-3 metabolism',
      ],
    };
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high':
        return 'text-error-600 bg-error-100';
      case 'moderate':
        return 'text-warning-600 bg-warning-100';
      case 'low':
        return 'text-primary-600 bg-primary-100';
      case 'protective':
        return 'text-secondary-600 bg-secondary-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getMetabolismColor = (status: string) => {
    switch (status) {
      case 'poor':
        return 'text-error-600';
      case 'intermediate':
        return 'text-warning-600';
      case 'normal':
        return 'text-secondary-600';
      case 'rapid':
      case 'ultrarapid':
        return 'text-primary-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="cosmic-card cosmic-card--primary mb-6"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <motion.div
              animate={{ 
                rotate: 360,
                scale: [1, 1.2, 1],
              }}
              transition={{ 
                rotate: { duration: 20, repeat: Infinity, ease: 'linear' },
                scale: { duration: 6, repeat: Infinity, ease: 'easeInOut' }
              }}
              className="w-16 h-16 cosmic-gradient rounded-full flex items-center justify-center"
            >
              <BeakerIcon className="w-8 h-8 text-white" />
            </motion.div>
            
            <div>
              <h1 className="text-3xl font-bold cosmic-gradient bg-clip-text text-transparent">
                🧬 Cosmic Genetic Analyzer
              </h1>
              <p className="text-gray-600 mt-1">
                Pharmacogenomic analysis for personalized supplement optimization
              </p>
            </div>
          </div>

          {/* Accuracy Indicator */}
          <div className="text-center">
            <div className="cosmic-health-score" style={{ width: '80px', height: '80px' }}>
              <div className="cosmic-health-score__value" style={{ fontSize: '14px' }}>
                {cosmicAccuracy.toFixed(1)}%
              </div>
            </div>
            <p className="text-sm text-gray-600 mt-2">AI Accuracy</p>
          </div>
        </div>
      </motion.div>

      {/* Processing Status */}
      <AnimatePresence>
        {isProcessing && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="cosmic-card mb-6 text-center"
          >
            <div className="flex items-center justify-center space-x-3 mb-4">
              <div className="cosmic-spin w-8 h-8 border-4 border-primary-200 border-t-primary-600 rounded-full"></div>
              <h3 className="text-xl font-semibold text-gray-900">Processing Genetic Data</h3>
            </div>
            <p className="text-gray-600 mb-4">{processingStage}</p>
            <div className="cosmic-progress">
              <motion.div 
                className="cosmic-progress__bar"
                animate={{ width: `${uploadProgress}%` }}
                transition={{ duration: 0.3 }}
              />
            </div>
            <p className="text-sm text-gray-500 mt-2">
              Analyzing {uploadProgress < 60 ? '847,392' : '847,392'} genetic variants...
            </p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Upload Section */}
      {!geneticReport && !isProcessing && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="cosmic-card text-center mb-6"
        >
          <BeakerIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Upload Your Genetic Data
          </h3>
          <p className="text-gray-600 mb-6">
            Upload raw genetic data from 23andMe, AncestryDNA, or other providers for personalized supplement recommendations
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="cosmic-card bg-gray-50">
              <DocumentTextIcon className="w-8 h-8 text-primary-600 mx-auto mb-2" />
              <h4 className="font-medium text-gray-900">Supported Formats</h4>
              <p className="text-sm text-gray-600">23andMe, AncestryDNA, VCF, CSV</p>
            </div>
            <div className="cosmic-card bg-gray-50">
              <ShieldCheckIcon className="w-8 h-8 text-secondary-600 mx-auto mb-2" />
              <h4 className="font-medium text-gray-900">Privacy Protected</h4>
              <p className="text-sm text-gray-600">End-to-end encryption, HIPAA compliant</p>
            </div>
            <div className="cosmic-card bg-gray-50">
              <CpuChipIcon className="w-8 h-8 text-warning-600 mx-auto mb-2" />
              <h4 className="font-medium text-gray-900">AI Analysis</h4>
              <p className="text-sm text-gray-600">99.7% accuracy, 847K+ variants</p>
            </div>
          </div>

          <button
            onClick={processGeneticData}
            className="cosmic-button cosmic-button--primary"
          >
            <SparklesIcon className="w-5 h-5 mr-2" />
            Demo Analysis (Sample Data)
          </button>
        </motion.div>
      )}

      {/* Genetic Report */}
      {geneticReport && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Metabolism Profile */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="cosmic-card"
          >
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900">
                🧬 Metabolism Profile
              </h2>
              <button
                onClick={() => setShowSensitiveData(!showSensitiveData)}
                className="cosmic-button cosmic-button--outline text-xs py-1 px-2"
              >
                <EyeIcon className="w-4 h-4" />
              </button>
            </div>

            <div className="space-y-3">
              {Object.entries(geneticReport.metabolismProfile).map(([gene, status]) => (
                <div key={gene} className="cosmic-card bg-gray-50">
                  <div className="flex items-center justify-between mb-1">
                    <span className="font-medium text-gray-900 uppercase">{gene}</span>
                    <span className={`text-sm font-medium ${getMetabolismColor(status)}`}>
                      {status}
                    </span>
                  </div>
                  <div className="text-xs text-gray-600">
                    {gene === 'cyp2d6' && 'Drug metabolism enzyme'}
                    {gene === 'cyp3a4' && 'Major drug metabolism pathway'}
                    {gene === 'mthfr' && 'Folate metabolism'}
                    {gene === 'comt' && 'Dopamine metabolism'}
                    {gene === 'apoe' && 'Alzheimer\'s risk factor'}
                  </div>
                </div>
              ))}
            </div>

            {/* Risk and Protective Factors */}
            <div className="mt-6">
              <h3 className="font-semibold text-gray-900 mb-3">Risk Factors</h3>
              <div className="space-y-2">
                {geneticReport.riskFactors.map((factor, index) => (
                  <div key={index} className="flex items-center space-x-2 text-sm">
                    <ExclamationTriangleIcon className="w-4 h-4 text-warning-600" />
                    <span className="text-gray-700">{factor}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="mt-4">
              <h3 className="font-semibold text-gray-900 mb-3">Protective Factors</h3>
              <div className="space-y-2">
                {geneticReport.protectiveFactors.map((factor, index) => (
                  <div key={index} className="flex items-center space-x-2 text-sm">
                    <CheckCircleIcon className="w-4 h-4 text-secondary-600" />
                    <span className="text-gray-700">{factor}</span>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Supplement Recommendations */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="cosmic-card"
            >
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                💊 Personalized Supplement Recommendations
              </h2>

              <div className="space-y-4">
                {geneticReport.supplementRecommendations.map((rec) => (
                  <div key={rec.id} className="cosmic-card cosmic-card--health">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-1">{rec.supplement}</h3>
                        <p className="text-sm text-gray-600">{rec.reasoning}</p>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-secondary-600">
                          {Math.round(rec.confidence * 100)}%
                        </div>
                        <div className="text-xs text-gray-500">Confidence</div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                      <div>
                        <span className="text-sm font-medium text-gray-700">Dosage:</span>
                        <p className="text-sm text-gray-600">{rec.dosageModification}</p>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-700">Timing:</span>
                        <p className="text-sm text-gray-600">{rec.timingRecommendation}</p>
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-2 mb-3">
                      {rec.supportingVariants.map(variant => (
                        <span key={variant} className="cosmic-badge cosmic-badge--primary text-xs">
                          {variant}
                        </span>
                      ))}
                    </div>

                    {rec.contraindications.length > 0 && (
                      <div className="bg-warning-50 border border-warning-200 rounded p-2">
                        <div className="flex items-center space-x-1 text-warning-700 text-sm">
                          <ExclamationTriangleIcon className="w-4 h-4" />
                          <span className="font-medium">Contraindications:</span>
                        </div>
                        <ul className="text-sm text-warning-600 mt-1">
                          {rec.contraindications.map((contra, index) => (
                            <li key={index}>• {contra}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      )}

      {/* Report Summary */}
      {geneticReport && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="cosmic-card mt-6"
        >
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            📊 Analysis Summary
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary-600">
                {geneticReport.variantsAnalyzed.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Variants Analyzed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-secondary-600">
                {geneticReport.supplementRecommendations.length}
              </div>
              <div className="text-sm text-gray-600">Recommendations</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-warning-600">
                {geneticReport.riskFactors.length}
              </div>
              <div className="text-sm text-gray-600">Risk Factors</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {geneticReport.protectiveFactors.length}
              </div>
              <div className="text-sm text-gray-600">Protective Factors</div>
            </div>
          </div>

          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Report generated: {geneticReport.uploadDate.toLocaleDateString()}
              </div>
              <div className="flex space-x-2">
                <button className="cosmic-button cosmic-button--outline">
                  <DocumentTextIcon className="w-4 h-4 mr-2" />
                  Download Report
                </button>
                <button className="cosmic-button cosmic-button--primary">
                  <ChartBarIcon className="w-4 h-4 mr-2" />
                  View Detailed Analysis
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default CosmicGeneticAnalyzer;
