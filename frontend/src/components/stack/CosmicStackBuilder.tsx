import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence, Reorder } from 'framer-motion';
import {
  BeakerIcon,
  SparklesIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  BoltIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  ClockIcon,
  TrashIcon,
  PlusIcon,
  AdjustmentsHorizontalIcon,
} from '@heroicons/react/24/outline';

interface StackSupplement {
  id: string;
  name: string;
  dosage: string;
  timing: string[];
  frequency: string;
  price: number;
  safetyRating: number;
  evidenceLevel: number;
  effects: string[];
  warnings: string[];
  interactions: string[];
}

interface StackAnalysis {
  overallSafetyScore: number;
  riskLevel: 'low' | 'moderate' | 'high' | 'critical';
  totalCost: number;
  monthlyCost: number;
  goalCoverage: number;
  interactions: Interaction[];
  recommendations: string[];
  optimizationScore: number;
}

interface Interaction {
  id: string;
  type: 'synergistic' | 'antagonistic' | 'neutral' | 'warning';
  supplements: string[];
  severity: 'mild' | 'moderate' | 'severe';
  description: string;
  recommendation: string;
}

interface CosmicStackBuilderProps {
  userId: string;
  initialStack?: StackSupplement[];
  onStackUpdate?: (stack: StackSupplement[]) => void;
}

const CosmicStackBuilder: React.FC<CosmicStackBuilderProps> = ({
  userId,
  initialStack = [],
  onStackUpdate,
}) => {
  const [stack, setStack] = useState<StackSupplement[]>(initialStack);
  const [analysis, setAnalysis] = useState<StackAnalysis | null>(null);
  const [loading, setLoading] = useState(false);
  const [showOptimization, setShowOptimization] = useState(false);
  const [optimizationGoals, setOptimizationGoals] = useState<string[]>([]);
  const [budget, setBudget] = useState(100);

  // Sample stack data
  const sampleStack: StackSupplement[] = [
    {
      id: 'vitamin-d3',
      name: 'Vitamin D3',
      dosage: '2000 IU',
      timing: ['morning'],
      frequency: 'daily',
      price: 15,
      safetyRating: 9,
      evidenceLevel: 5,
      effects: ['Bone Health', 'Immune Support'],
      warnings: ['Monitor calcium levels'],
      interactions: ['magnesium'],
    },
    {
      id: 'magnesium-glycinate',
      name: 'Magnesium Glycinate',
      dosage: '400mg',
      timing: ['evening'],
      frequency: 'daily',
      price: 25,
      safetyRating: 8,
      evidenceLevel: 4,
      effects: ['Sleep Quality', 'Muscle Relaxation'],
      warnings: ['May cause drowsiness'],
      interactions: ['vitamin-d3'],
    },
    {
      id: 'omega-3',
      name: 'Omega-3 EPA/DHA',
      dosage: '1000mg',
      timing: ['morning', 'evening'],
      frequency: 'daily',
      price: 35,
      safetyRating: 9,
      evidenceLevel: 5,
      effects: ['Brain Health', 'Heart Health'],
      warnings: ['Blood thinning effects'],
      interactions: [],
    },
  ];

  useEffect(() => {
    if (stack.length === 0) {
      setStack(sampleStack);
    }
  }, []);

  useEffect(() => {
    if (stack.length > 0) {
      analyzeStack();
    }
    onStackUpdate?.(stack);
  }, [stack]);

  const analyzeStack = useCallback(async () => {
    setLoading(true);
    
    try {
      // Simulate AI analysis
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const totalCost = stack.reduce((sum, supplement) => sum + supplement.price, 0);
      const avgSafety = stack.reduce((sum, supplement) => sum + supplement.safetyRating, 0) / stack.length;
      const avgEvidence = stack.reduce((sum, supplement) => sum + supplement.evidenceLevel, 0) / stack.length;
      
      // Calculate interactions
      const interactions: Interaction[] = [];
      for (let i = 0; i < stack.length; i++) {
        for (let j = i + 1; j < stack.length; j++) {
          const supp1 = stack[i];
          const supp2 = stack[j];
          
          if (supp1.interactions.includes(supp2.id) || supp2.interactions.includes(supp1.id)) {
            interactions.push({
              id: `${supp1.id}-${supp2.id}`,
              type: 'synergistic',
              supplements: [supp1.name, supp2.name],
              severity: 'mild',
              description: `${supp1.name} and ${supp2.name} work synergistically together`,
              recommendation: 'Take together for enhanced benefits',
            });
          }
        }
      }

      const analysis: StackAnalysis = {
        overallSafetyScore: Math.round(avgSafety * 10),
        riskLevel: avgSafety >= 8 ? 'low' : avgSafety >= 6 ? 'moderate' : 'high',
        totalCost,
        monthlyCost: totalCost,
        goalCoverage: 85,
        interactions,
        recommendations: [
          'Consider taking Vitamin D3 with Magnesium for better absorption',
          'Omega-3 timing is optimal for maximum bioavailability',
          'Your stack covers most essential health areas',
        ],
        optimizationScore: Math.round((avgSafety + avgEvidence + (100 - totalCost / 2)) / 3),
      };

      setAnalysis(analysis);
    } catch (error) {
      console.error('Error analyzing stack:', error);
    } finally {
      setLoading(false);
    }
  }, [stack]);

  const addSupplement = (supplement: StackSupplement) => {
    setStack(prev => [...prev, supplement]);
  };

  const removeSupplement = (id: string) => {
    setStack(prev => prev.filter(s => s.id !== id));
  };

  const updateSupplement = (id: string, updates: Partial<StackSupplement>) => {
    setStack(prev => prev.map(s => s.id === id ? { ...s, ...updates } : s));
  };

  const optimizeStack = async () => {
    setLoading(true);
    
    try {
      // Simulate AI optimization
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Apply optimizations (this would be real AI logic)
      const optimizedStack = stack.map(supplement => ({
        ...supplement,
        dosage: supplement.dosage, // AI would optimize dosages
        timing: supplement.timing, // AI would optimize timing
      }));
      
      setStack(optimizedStack);
    } catch (error) {
      console.error('Error optimizing stack:', error);
    } finally {
      setLoading(false);
    }
  };

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'low': return 'text-secondary-600';
      case 'moderate': return 'text-warning-600';
      case 'high': return 'text-error-600';
      case 'critical': return 'text-red-700';
      default: return 'text-gray-600';
    }
  };

  const getInteractionColor = (type: string) => {
    switch (type) {
      case 'synergistic': return 'border-secondary-200 bg-secondary-50';
      case 'antagonistic': return 'border-error-200 bg-error-50';
      case 'warning': return 'border-warning-200 bg-warning-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="cosmic-card mb-6"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold cosmic-gradient bg-clip-text text-transparent">
              🧪 Cosmic Stack Builder
            </h1>
            <p className="text-gray-600 mt-2">
              Build and optimize your perfect supplement stack with AI-powered insights
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setShowOptimization(!showOptimization)}
              className="cosmic-button cosmic-button--outline"
            >
              <AdjustmentsHorizontalIcon className="w-4 h-4 mr-2" />
              Optimize
            </button>
            <button
              onClick={optimizeStack}
              disabled={loading}
              className="cosmic-button cosmic-button--primary"
            >
              <BoltIcon className="w-4 h-4 mr-2" />
              AI Optimize
            </button>
          </div>
        </div>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Stack Builder */}
        <div className="lg:col-span-2 space-y-6">
          {/* Current Stack */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="cosmic-card"
          >
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900">Your Stack</h2>
              <div className="flex items-center space-x-2">
                <span className="cosmic-badge cosmic-badge--primary">
                  {stack.length} Supplements
                </span>
                <button className="cosmic-button cosmic-button--outline">
                  <PlusIcon className="w-4 h-4 mr-2" />
                  Add
                </button>
              </div>
            </div>

            <Reorder.Group
              axis="y"
              values={stack}
              onReorder={setStack}
              className="space-y-3"
            >
              <AnimatePresence>
                {stack.map((supplement) => (
                  <Reorder.Item
                    key={supplement.id}
                    value={supplement}
                    className="cosmic-card cosmic-card--health cursor-move"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <BeakerIcon className="w-5 h-5 text-primary-500" />
                          <h3 className="font-semibold text-gray-900">{supplement.name}</h3>
                          <span className="cosmic-badge cosmic-badge--success text-xs">
                            {supplement.dosage}
                          </span>
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                          <div>
                            <span className="text-gray-500">Timing:</span>
                            <span className="ml-1 font-medium">
                              {supplement.timing.join(', ')}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-500">Frequency:</span>
                            <span className="ml-1 font-medium">{supplement.frequency}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">Safety:</span>
                            <span className="ml-1 font-medium text-secondary-600">
                              {supplement.safetyRating}/10
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-500">Price:</span>
                            <span className="ml-1 font-medium">${supplement.price}</span>
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-1 mt-2">
                          {supplement.effects.map(effect => (
                            <span
                              key={effect}
                              className="cosmic-badge cosmic-badge--primary text-xs"
                            >
                              {effect}
                            </span>
                          ))}
                        </div>
                      </div>

                      <button
                        onClick={() => removeSupplement(supplement.id)}
                        className="text-gray-400 hover:text-error-500 transition-colors ml-4"
                      >
                        <TrashIcon className="w-5 h-5" />
                      </button>
                    </div>
                  </Reorder.Item>
                ))}
              </AnimatePresence>
            </Reorder.Group>

            {stack.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <BeakerIcon className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                <p>No supplements in your stack yet</p>
                <p className="text-sm">Add supplements to get started</p>
              </div>
            )}
          </motion.div>

          {/* Interactions */}
          {analysis && analysis.interactions.length > 0 && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="cosmic-card"
            >
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Interactions</h2>
              <div className="space-y-3">
                {analysis.interactions.map(interaction => (
                  <div
                    key={interaction.id}
                    className={`p-3 rounded-lg border ${getInteractionColor(interaction.type)}`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-gray-900">
                          {interaction.supplements.join(' + ')}
                        </span>
                        <span className={`cosmic-badge ${
                          interaction.type === 'synergistic' ? 'cosmic-badge--success' :
                          interaction.type === 'warning' ? 'cosmic-badge--warning' :
                          'cosmic-badge--error'
                        } text-xs`}>
                          {interaction.type}
                        </span>
                      </div>
                      <span className="text-sm text-gray-500 capitalize">
                        {interaction.severity}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-1">{interaction.description}</p>
                    <p className="text-sm font-medium text-gray-700">{interaction.recommendation}</p>
                  </div>
                ))}
              </div>
            </motion.div>
          )}
        </div>

        {/* Analysis Panel */}
        <div className="space-y-6">
          {/* Stack Analysis */}
          {analysis && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="cosmic-card cosmic-card--primary"
            >
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Stack Analysis</h2>
              
              {loading ? (
                <div className="text-center py-8">
                  <div className="cosmic-spin w-8 h-8 border-2 border-primary-200 border-t-primary-600 rounded-full mx-auto mb-3"></div>
                  <p className="text-gray-600">Analyzing your stack...</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Overall Score */}
                  <div className="text-center">
                    <div className="cosmic-health-score mx-auto mb-2">
                      <div className="cosmic-health-score__value">
                        {analysis.optimizationScore}
                      </div>
                    </div>
                    <p className="text-sm text-gray-600">Optimization Score</p>
                  </div>

                  {/* Metrics */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className={`text-2xl font-bold ${getRiskColor(analysis.riskLevel)}`}>
                        {analysis.overallSafetyScore}
                      </div>
                      <div className="text-xs text-gray-500">Safety Score</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-secondary-600">
                        {analysis.goalCoverage}%
                      </div>
                      <div className="text-xs text-gray-500">Goal Coverage</div>
                    </div>
                  </div>

                  {/* Cost Analysis */}
                  <div className="cosmic-card bg-gray-50">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700">Monthly Cost</span>
                      <span className="text-lg font-bold text-primary-600">
                        ${analysis.monthlyCost}
                      </span>
                    </div>
                    <div className="cosmic-progress">
                      <div 
                        className="cosmic-progress__bar"
                        style={{ width: `${Math.min((analysis.monthlyCost / budget) * 100, 100)}%` }}
                      />
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      Budget: ${budget}/month
                    </div>
                  </div>

                  {/* Risk Level */}
                  <div className="flex items-center space-x-2">
                    <ShieldCheckIcon className={`w-5 h-5 ${getRiskColor(analysis.riskLevel)}`} />
                    <span className="text-sm font-medium text-gray-700">Risk Level:</span>
                    <span className={`text-sm font-bold capitalize ${getRiskColor(analysis.riskLevel)}`}>
                      {analysis.riskLevel}
                    </span>
                  </div>
                </div>
              )}
            </motion.div>
          )}

          {/* Recommendations */}
          {analysis && analysis.recommendations.length > 0 && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="cosmic-card"
            >
              <h2 className="text-lg font-semibold text-gray-900 mb-3">AI Recommendations</h2>
              <div className="space-y-2">
                {analysis.recommendations.map((recommendation, index) => (
                  <div key={index} className="flex items-start space-x-2">
                    <SparklesIcon className="w-4 h-4 text-primary-500 mt-0.5 flex-shrink-0" />
                    <p className="text-sm text-gray-600">{recommendation}</p>
                  </div>
                ))}
              </div>
            </motion.div>
          )}

          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="cosmic-card"
          >
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Quick Actions</h2>
            <div className="space-y-2">
              <button className="cosmic-button cosmic-button--outline w-full">
                <ChartBarIcon className="w-4 h-4 mr-2" />
                Generate Report
              </button>
              <button className="cosmic-button cosmic-button--outline w-full">
                <ClockIcon className="w-4 h-4 mr-2" />
                Set Reminders
              </button>
              <button className="cosmic-button cosmic-button--outline w-full">
                <CurrencyDollarIcon className="w-4 h-4 mr-2" />
                Find Deals
              </button>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default CosmicStackBuilder;
