import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  DocumentTextIcon,
  BeakerIcon,
  ChartBarIcon,
  ClockIcon,
  GlobeAltIcon,
  CheckBadgeIcon,
  ExclamationTriangleIcon,
  SparklesIcon,
  BoltIcon,
} from '@heroicons/react/24/outline';

interface ResearchSource {
  id: string;
  name: string;
  type: 'pubmed' | 'cochrane' | 'clinicaltrials' | 'fda' | 'ema' | 'health_canada';
  articles: number;
  lastUpdate: Date;
  status: 'active' | 'processing' | 'error';
  processingSpeed: number; // articles per second
}

interface ResearchArticle {
  id: string;
  title: string;
  authors: string[];
  journal: string;
  year: number;
  pmid?: string;
  doi?: string;
  abstract: string;
  relevanceScore: number;
  qualityScore: number;
  extractedData: ExtractedData;
  processingTime: number;
  confidence: number;
}

interface ExtractedData {
  supplements: string[];
  conditions: string[];
  outcomes: string[];
  dosages: string[];
  sideEffects: string[];
  interactions: string[];
  studyType: 'rct' | 'observational' | 'meta_analysis' | 'review' | 'case_study';
  participants: number;
  duration: string;
  conclusions: string[];
}

interface ResearchSingularityEngineProps {
  searchQuery?: string;
  autoUpdate?: boolean;
  onNewResearch?: (article: ResearchArticle) => void;
}

const ResearchSingularityEngine: React.FC<ResearchSingularityEngineProps> = ({
  searchQuery = '',
  autoUpdate = true,
  onNewResearch,
}) => {
  const [sources, setSources] = useState<ResearchSource[]>([]);
  const [recentArticles, setRecentArticles] = useState<ResearchArticle[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStats, setProcessingStats] = useState({
    articlesProcessed: 0,
    totalSources: 6,
    averageProcessingTime: 25, // seconds
    accuracy: 0.95,
  });
  const [searchInput, setSearchInput] = useState(searchQuery);

  // Initialize research sources
  useEffect(() => {
    initializeResearchSources();
  }, []);

  // Auto-update research
  useEffect(() => {
    if (autoUpdate) {
      const interval = setInterval(() => {
        processNewResearch();
      }, 30000); // Every 30 seconds

      return () => clearInterval(interval);
    }
  }, [autoUpdate]);

  const initializeResearchSources = () => {
    const initialSources: ResearchSource[] = [
      {
        id: 'pubmed',
        name: 'PubMed/MEDLINE',
        type: 'pubmed',
        articles: 35000000,
        lastUpdate: new Date(),
        status: 'active',
        processingSpeed: 150,
      },
      {
        id: 'cochrane',
        name: 'Cochrane Library',
        type: 'cochrane',
        articles: 850000,
        lastUpdate: new Date(),
        status: 'active',
        processingSpeed: 80,
      },
      {
        id: 'clinicaltrials',
        name: 'ClinicalTrials.gov',
        type: 'clinicaltrials',
        articles: 400000,
        lastUpdate: new Date(),
        status: 'active',
        processingSpeed: 120,
      },
      {
        id: 'fda',
        name: 'FDA Database',
        type: 'fda',
        articles: 250000,
        lastUpdate: new Date(),
        status: 'active',
        processingSpeed: 90,
      },
      {
        id: 'ema',
        name: 'EMA Database',
        type: 'ema',
        articles: 180000,
        lastUpdate: new Date(),
        status: 'active',
        processingSpeed: 75,
      },
      {
        id: 'health_canada',
        name: 'Health Canada',
        type: 'health_canada',
        articles: 120000,
        lastUpdate: new Date(),
        status: 'active',
        processingSpeed: 60,
      },
    ];

    setSources(initialSources);
  };

  const processNewResearch = useCallback(async () => {
    if (isProcessing) return;

    setIsProcessing(true);
    
    try {
      // Simulate real-time research processing
      const processingPromises = sources.map(async (source) => {
        // Update source status
        setSources(prev => prev.map(s => 
          s.id === source.id ? { ...s, status: 'processing' as const } : s
        ));

        // Simulate processing time
        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

        // Generate sample research article
        const article = await generateSampleArticle(source);
        
        // Update source status
        setSources(prev => prev.map(s => 
          s.id === source.id ? { 
            ...s, 
            status: 'active' as const,
            lastUpdate: new Date()
          } : s
        ));

        return article;
      });

      const newArticles = await Promise.all(processingPromises);
      
      // Add new articles to the list
      setRecentArticles(prev => [...newArticles, ...prev].slice(0, 20));
      
      // Update processing stats
      setProcessingStats(prev => ({
        ...prev,
        articlesProcessed: prev.articlesProcessed + newArticles.length,
      }));

      // Notify parent component
      newArticles.forEach(article => {
        onNewResearch?.(article);
      });

    } catch (error) {
      console.error('Error processing research:', error);
    } finally {
      setIsProcessing(false);
    }
  }, [sources, onNewResearch, isProcessing]);

  const generateSampleArticle = async (source: ResearchSource): Promise<ResearchArticle> => {
    const sampleTitles = [
      'Vitamin D3 supplementation and immune function: A randomized controlled trial',
      'Magnesium glycinate effects on sleep quality in adults with insomnia',
      'Omega-3 fatty acids and cognitive performance: Meta-analysis of 15 studies',
      'Ashwagandha root extract for stress reduction: Clinical evidence',
      'Curcumin bioavailability enhancement with piperine: Pharmacokinetic study',
      'Probiotics and gut microbiome diversity: Systematic review',
    ];

    const sampleJournals = [
      'Journal of Clinical Medicine',
      'Nutrients',
      'American Journal of Clinical Nutrition',
      'Phytotherapy Research',
      'Clinical Nutrition',
      'Frontiers in Nutrition',
    ];

    const processingTime = Math.random() * 20 + 10; // 10-30 seconds

    return {
      id: `article-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      title: sampleTitles[Math.floor(Math.random() * sampleTitles.length)],
      authors: ['Smith J.', 'Johnson A.', 'Williams B.'],
      journal: sampleJournals[Math.floor(Math.random() * sampleJournals.length)],
      year: 2024,
      pmid: `${38000000 + Math.floor(Math.random() * 1000000)}`,
      doi: `10.1000/${Math.random().toString(36).substr(2, 9)}`,
      abstract: 'This study investigates the effects of supplement intervention on health outcomes...',
      relevanceScore: 0.8 + Math.random() * 0.2,
      qualityScore: 0.7 + Math.random() * 0.3,
      extractedData: {
        supplements: ['Vitamin D3', 'Magnesium'],
        conditions: ['Immune dysfunction', 'Sleep disorders'],
        outcomes: ['Improved immune markers', 'Better sleep quality'],
        dosages: ['2000 IU daily', '400mg before bed'],
        sideEffects: ['Mild gastrointestinal upset'],
        interactions: ['None reported'],
        studyType: 'rct',
        participants: Math.floor(Math.random() * 1000) + 100,
        duration: '12 weeks',
        conclusions: ['Significant improvement in primary outcomes'],
      },
      processingTime,
      confidence: 0.9 + Math.random() * 0.1,
    };
  };

  const handleSearch = async () => {
    if (!searchInput.trim()) return;

    setIsProcessing(true);
    
    try {
      // Simulate targeted search
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Generate search results
      const searchResults = await Promise.all(
        sources.slice(0, 3).map(source => generateSampleArticle(source))
      );
      
      setRecentArticles(searchResults);
      
    } catch (error) {
      console.error('Error searching research:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const getSourceIcon = (type: string) => {
    switch (type) {
      case 'pubmed':
        return '🔬';
      case 'cochrane':
        return '📊';
      case 'clinicaltrials':
        return '🧪';
      case 'fda':
        return '🏛️';
      case 'ema':
        return '🇪🇺';
      case 'health_canada':
        return '🇨🇦';
      default:
        return '📄';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-secondary-600 bg-secondary-100';
      case 'processing':
        return 'text-warning-600 bg-warning-100';
      case 'error':
        return 'text-error-600 bg-error-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStudyTypeIcon = (type: string) => {
    switch (type) {
      case 'rct':
        return '🎯';
      case 'meta_analysis':
        return '📈';
      case 'observational':
        return '👁️';
      case 'review':
        return '📚';
      case 'case_study':
        return '📋';
      default:
        return '📄';
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="cosmic-card cosmic-card--primary mb-6"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <motion.div
              animate={{ 
                rotate: 360,
                scale: [1, 1.2, 1],
              }}
              transition={{ 
                rotate: { duration: 8, repeat: Infinity, ease: 'linear' },
                scale: { duration: 3, repeat: Infinity, ease: 'easeInOut' }
              }}
              className="w-16 h-16 cosmic-gradient rounded-full flex items-center justify-center"
            >
              <GlobeAltIcon className="w-8 h-8 text-white" />
            </motion.div>
            
            <div>
              <h1 className="text-3xl font-bold cosmic-gradient bg-clip-text text-transparent">
                🌐 Research Singularity Engine
              </h1>
              <p className="text-gray-600 mt-1">
                Real-time integration of 35M+ research articles with &lt;30s processing
              </p>
            </div>
          </div>

          {/* Processing Stats */}
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-secondary-600">
                {processingStats.articlesProcessed}
              </div>
              <div className="text-xs text-gray-500">Articles Processed</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-primary-600">
                {processingStats.averageProcessingTime}s
              </div>
              <div className="text-xs text-gray-500">Avg Processing</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-warning-600">
                {Math.round(processingStats.accuracy * 100)}%
              </div>
              <div className="text-xs text-gray-500">AI Accuracy</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-600">
                {processingStats.totalSources}
              </div>
              <div className="text-xs text-gray-500">Data Sources</div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Search Interface */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="cosmic-card mb-6"
      >
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          🔍 Quantum Research Search
        </h2>
        
        <div className="flex space-x-4">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search 35M+ research articles..."
              className="cosmic-input pl-10"
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
          </div>
          <button
            onClick={handleSearch}
            disabled={isProcessing}
            className="cosmic-button cosmic-button--primary"
          >
            <BoltIcon className="w-5 h-5 mr-2" />
            Search
          </button>
          <button
            onClick={processNewResearch}
            disabled={isProcessing}
            className="cosmic-button cosmic-button--outline"
          >
            <SparklesIcon className="w-5 h-5 mr-2" />
            Update
          </button>
        </div>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Data Sources */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className="cosmic-card"
        >
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            📡 Data Sources
          </h2>
          
          <div className="space-y-3">
            {sources.map((source) => (
              <div key={source.id} className="cosmic-card bg-gray-50">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{getSourceIcon(source.type)}</span>
                    <span className="font-medium text-gray-900">{source.name}</span>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(source.status)}`}>
                    {source.status}
                  </span>
                </div>
                
                <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
                  <div>
                    <span className="font-medium">{source.articles.toLocaleString()}</span>
                    <span className="text-gray-500"> articles</span>
                  </div>
                  <div>
                    <span className="font-medium">{source.processingSpeed}</span>
                    <span className="text-gray-500"> /sec</span>
                  </div>
                </div>
                
                {source.status === 'processing' && (
                  <div className="mt-2">
                    <div className="cosmic-progress">
                      <motion.div 
                        className="cosmic-progress__bar"
                        initial={{ width: 0 }}
                        animate={{ width: '100%' }}
                        transition={{ duration: 3, ease: 'easeInOut' }}
                      />
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </motion.div>

        {/* Recent Research */}
        <div className="lg:col-span-2">
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
            className="cosmic-card"
          >
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900">
                📚 Latest Research
              </h2>
              {isProcessing && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <div className="cosmic-spin w-4 h-4 border-2 border-primary-200 border-t-primary-600 rounded-full"></div>
                  <span>Processing...</span>
                </div>
              )}
            </div>

            <div className="space-y-4 max-h-96 overflow-y-auto">
              <AnimatePresence>
                {recentArticles.map((article, index) => (
                  <motion.div
                    key={article.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ delay: index * 0.1 }}
                    className="cosmic-card cosmic-card--health"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 mb-1 line-clamp-2">
                          {article.title}
                        </h3>
                        <div className="flex items-center space-x-2 text-sm text-gray-600 mb-2">
                          <span>{getStudyTypeIcon(article.extractedData.studyType)}</span>
                          <span>{article.journal}</span>
                          <span>•</span>
                          <span>{article.year}</span>
                          <span>•</span>
                          <span>{article.extractedData.participants} participants</span>
                        </div>
                      </div>
                      
                      <div className="text-right ml-4">
                        <div className="text-lg font-bold text-secondary-600">
                          {Math.round(article.relevanceScore * 100)}%
                        </div>
                        <div className="text-xs text-gray-500">Relevance</div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Supplements:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {article.extractedData.supplements.slice(0, 2).map((supp, idx) => (
                            <span key={idx} className="cosmic-badge cosmic-badge--primary text-xs">
                              {supp}
                            </span>
                          ))}
                        </div>
                      </div>
                      
                      <div>
                        <span className="font-medium text-gray-700">Outcomes:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {article.extractedData.outcomes.slice(0, 2).map((outcome, idx) => (
                            <span key={idx} className="cosmic-badge cosmic-badge--success text-xs">
                              {outcome}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-200">
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <div className="flex items-center space-x-1">
                          <ClockIcon className="w-3 h-3" />
                          <span>{article.processingTime.toFixed(1)}s</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <CheckBadgeIcon className="w-3 h-3" />
                          <span>{Math.round(article.confidence * 100)}% confidence</span>
                        </div>
                      </div>
                      
                      <button className="cosmic-button cosmic-button--outline text-xs py-1 px-2">
                        View Details
                      </button>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>

            {recentArticles.length === 0 && !isProcessing && (
              <div className="text-center py-8 text-gray-500">
                <DocumentTextIcon className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                <p>No research articles yet</p>
                <p className="text-sm">Start processing to see latest research</p>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default ResearchSingularityEngine;
