import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  CloudIcon,
  CpuChipIcon,
  ChartBarIcon,
  GlobeAltIcon,
  ServerIcon,
  BoltIcon,
  ArrowTrendingUpIcon,
  Cog6ToothIcon,
  SignalIcon,
  RocketLaunchIcon,
} from '@heroicons/react/24/outline';

interface ScalabilityMetrics {
  concurrentUsers: number;
  requestsPerSecond: number;
  dataProcessing: string;
  aiPredictions: number;
  globalRegions: number;
  languagesSupported: number;
  uptime: number;
  responseTime: number;
}

interface RegionStatus {
  id: string;
  name: string;
  location: string;
  status: 'active' | 'maintenance' | 'offline';
  users: number;
  latency: number;
  capacity: number;
  load: number;
}

interface MicroserviceStatus {
  id: string;
  name: string;
  type: 'ai' | 'data' | 'ui' | 'auth' | 'search' | 'graph';
  status: 'healthy' | 'warning' | 'error';
  instances: number;
  cpu: number;
  memory: number;
  requests: number;
  errors: number;
}

interface InfiniteScalabilityCenterProps {
  onScaleEvent?: (event: any) => void;
}

const InfiniteScalabilityCenter: React.FC<InfiniteScalabilityCenterProps> = ({
  onScaleEvent,
}) => {
  const [metrics, setMetrics] = useState<ScalabilityMetrics>({
    concurrentUsers: 2847392,
    requestsPerSecond: 156789,
    dataProcessing: '47.3TB/day',
    aiPredictions: 8934567,
    globalRegions: 15,
    languagesSupported: 25,
    uptime: 99.99,
    responseTime: 23,
  });

  const [regions, setRegions] = useState<RegionStatus[]>([]);
  const [microservices, setMicroservices] = useState<MicroserviceStatus[]>([]);
  const [isAutoScaling, setIsAutoScaling] = useState(true);
  const [scalingEvents, setScalingEvents] = useState<any[]>([]);
  const [cosmicPower, setCosmicPower] = useState(97);

  useEffect(() => {
    initializeScalabilityCenter();
  }, []);

  useEffect(() => {
    // Real-time metrics updates
    const metricsInterval = setInterval(() => {
      updateMetrics();
    }, 2000);

    // Cosmic power fluctuation
    const powerInterval = setInterval(() => {
      setCosmicPower(prev => {
        const fluctuation = (Math.random() - 0.5) * 5;
        return Math.max(90, Math.min(100, prev + fluctuation));
      });
    }, 3000);

    return () => {
      clearInterval(metricsInterval);
      clearInterval(powerInterval);
    };
  }, []);

  const initializeScalabilityCenter = () => {
    // Initialize global regions
    const initialRegions: RegionStatus[] = [
      {
        id: 'us-east-1',
        name: 'US East (N. Virginia)',
        location: 'Virginia, USA',
        status: 'active',
        users: 456789,
        latency: 12,
        capacity: 1000000,
        load: 45.6,
      },
      {
        id: 'eu-central-1',
        name: 'EU Central (Frankfurt)',
        location: 'Frankfurt, Germany',
        status: 'active',
        users: 387654,
        latency: 8,
        capacity: 800000,
        load: 48.5,
      },
      {
        id: 'ap-southeast-1',
        name: 'Asia Pacific (Singapore)',
        location: 'Singapore',
        status: 'active',
        users: 298765,
        latency: 15,
        capacity: 600000,
        load: 49.8,
      },
      {
        id: 'us-west-2',
        name: 'US West (Oregon)',
        location: 'Oregon, USA',
        status: 'active',
        users: 234567,
        latency: 10,
        capacity: 700000,
        load: 33.5,
      },
      {
        id: 'eu-west-1',
        name: 'EU West (Ireland)',
        location: 'Dublin, Ireland',
        status: 'active',
        users: 345678,
        latency: 9,
        capacity: 750000,
        load: 46.1,
      },
    ];

    setRegions(initialRegions);

    // Initialize microservices
    const initialMicroservices: MicroserviceStatus[] = [
      {
        id: 'ai-service',
        name: 'AI Prediction Engine',
        type: 'ai',
        status: 'healthy',
        instances: 24,
        cpu: 67,
        memory: 72,
        requests: 45678,
        errors: 0,
      },
      {
        id: 'graph-service',
        name: 'Knowledge Graph API',
        type: 'graph',
        status: 'healthy',
        instances: 18,
        cpu: 54,
        memory: 68,
        requests: 34567,
        errors: 2,
      },
      {
        id: 'data-service',
        name: 'Data Processing Pipeline',
        type: 'data',
        status: 'healthy',
        instances: 32,
        cpu: 78,
        memory: 81,
        requests: 67890,
        errors: 1,
      },
      {
        id: 'search-service',
        name: 'Supplement Search Engine',
        type: 'search',
        status: 'warning',
        instances: 12,
        cpu: 89,
        memory: 85,
        requests: 23456,
        errors: 5,
      },
      {
        id: 'auth-service',
        name: 'Authentication Service',
        type: 'auth',
        status: 'healthy',
        instances: 8,
        cpu: 34,
        memory: 42,
        requests: 12345,
        errors: 0,
      },
      {
        id: 'ui-service',
        name: 'Frontend CDN',
        type: 'ui',
        status: 'healthy',
        instances: 48,
        cpu: 23,
        memory: 35,
        requests: 89012,
        errors: 3,
      },
    ];

    setMicroservices(initialMicroservices);
  };

  const updateMetrics = useCallback(() => {
    setMetrics(prev => ({
      ...prev,
      concurrentUsers: prev.concurrentUsers + Math.floor((Math.random() - 0.5) * 10000),
      requestsPerSecond: prev.requestsPerSecond + Math.floor((Math.random() - 0.5) * 5000),
      aiPredictions: prev.aiPredictions + Math.floor(Math.random() * 1000),
      responseTime: Math.max(10, prev.responseTime + (Math.random() - 0.5) * 5),
    }));

    // Update region loads
    setRegions(prev => prev.map(region => ({
      ...region,
      load: Math.max(20, Math.min(90, region.load + (Math.random() - 0.5) * 10)),
      users: region.users + Math.floor((Math.random() - 0.5) * 1000),
      latency: Math.max(5, region.latency + (Math.random() - 0.5) * 3),
    })));

    // Update microservice metrics
    setMicroservices(prev => prev.map(service => ({
      ...service,
      cpu: Math.max(10, Math.min(95, service.cpu + (Math.random() - 0.5) * 10)),
      memory: Math.max(15, Math.min(90, service.memory + (Math.random() - 0.5) * 8)),
      requests: service.requests + Math.floor(Math.random() * 1000),
    })));
  }, []);

  const triggerAutoScale = useCallback(async () => {
    const scalingEvent = {
      id: `scale-${Date.now()}`,
      type: 'auto_scale',
      timestamp: new Date(),
      description: 'Auto-scaling triggered due to increased load',
      region: 'Global',
      action: 'Scale up AI prediction instances',
    };

    setScalingEvents(prev => [scalingEvent, ...prev].slice(0, 10));
    onScaleEvent?.(scalingEvent);

    // Simulate scaling
    setMicroservices(prev => prev.map(service => 
      service.type === 'ai' ? { ...service, instances: service.instances + 4 } : service
    ));
  }, [onScaleEvent]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'active':
        return 'text-secondary-600 bg-secondary-100';
      case 'warning':
      case 'maintenance':
        return 'text-warning-600 bg-warning-100';
      case 'error':
      case 'offline':
        return 'text-error-600 bg-error-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getServiceTypeIcon = (type: string) => {
    switch (type) {
      case 'ai':
        return <CpuChipIcon className="w-4 h-4" />;
      case 'graph':
        return <ChartBarIcon className="w-4 h-4" />;
      case 'data':
        return <ServerIcon className="w-4 h-4" />;
      case 'search':
        return <SignalIcon className="w-4 h-4" />;
      case 'auth':
        return <Cog6ToothIcon className="w-4 h-4" />;
      case 'ui':
        return <GlobeAltIcon className="w-4 h-4" />;
      default:
        return <CloudIcon className="w-4 h-4" />;
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="cosmic-card cosmic-card--primary mb-6"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <motion.div
              animate={{ 
                rotate: 360,
                scale: [1, 1.2, 1],
              }}
              transition={{ 
                rotate: { duration: 15, repeat: Infinity, ease: 'linear' },
                scale: { duration: 5, repeat: Infinity, ease: 'easeInOut' }
              }}
              className="w-16 h-16 cosmic-gradient rounded-full flex items-center justify-center"
            >
              <RocketLaunchIcon className="w-8 h-8 text-white" />
            </motion.div>
            
            <div>
              <h1 className="text-3xl font-bold cosmic-gradient bg-clip-text text-transparent">
                🚀 Infinite Scalability Center
              </h1>
              <p className="text-gray-600 mt-1">
                Cloud-native architecture for 100M+ concurrent users
              </p>
            </div>
          </div>

          {/* Cosmic Power Indicator */}
          <div className="text-center">
            <div className="cosmic-health-score" style={{ width: '80px', height: '80px' }}>
              <div className="cosmic-health-score__value" style={{ fontSize: '16px' }}>
                {Math.round(cosmicPower)}%
              </div>
            </div>
            <p className="text-sm text-gray-600 mt-2">Cosmic Power</p>
          </div>
        </div>
      </motion.div>

      {/* Global Metrics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4 mb-6"
      >
        {[
          { label: 'Concurrent Users', value: formatNumber(metrics.concurrentUsers), icon: '👥', color: 'primary' },
          { label: 'Requests/sec', value: formatNumber(metrics.requestsPerSecond), icon: '⚡', color: 'secondary' },
          { label: 'Data Processing', value: metrics.dataProcessing, icon: '💾', color: 'warning' },
          { label: 'AI Predictions', value: formatNumber(metrics.aiPredictions), icon: '🧠', color: 'purple' },
          { label: 'Global Regions', value: metrics.globalRegions.toString(), icon: '🌍', color: 'indigo' },
          { label: 'Languages', value: metrics.languagesSupported.toString(), icon: '🗣️', color: 'pink' },
          { label: 'Uptime', value: `${metrics.uptime}%`, icon: '⏱️', color: 'green' },
          { label: 'Response Time', value: `${Math.round(metrics.responseTime)}ms`, icon: '🚀', color: 'blue' },
        ].map((metric, index) => (
          <motion.div
            key={metric.label}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 0.05 }}
            className="cosmic-card text-center"
          >
            <div className="text-2xl mb-1">{metric.icon}</div>
            <div className="text-lg font-bold text-primary-600">{metric.value}</div>
            <div className="text-xs text-gray-500">{metric.label}</div>
          </motion.div>
        ))}
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Global Regions */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className="cosmic-card"
        >
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            🌍 Global Regions
          </h2>

          <div className="space-y-3">
            {regions.map((region) => (
              <div key={region.id} className="cosmic-card cosmic-card--health">
                <div className="flex items-center justify-between mb-2">
                  <div>
                    <span className="font-medium text-gray-900">{region.name}</span>
                    <p className="text-sm text-gray-600">{region.location}</p>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(region.status)}`}>
                    {region.status}
                  </span>
                </div>

                <div className="grid grid-cols-3 gap-2 text-sm mb-2">
                  <div>
                    <span className="text-gray-500">Users:</span>
                    <span className="ml-1 font-medium">{formatNumber(region.users)}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Latency:</span>
                    <span className="ml-1 font-medium">{region.latency}ms</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Load:</span>
                    <span className="ml-1 font-medium">{region.load.toFixed(1)}%</span>
                  </div>
                </div>

                <div className="cosmic-progress">
                  <div 
                    className="cosmic-progress__bar"
                    style={{ width: `${region.load}%` }}
                  />
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Microservices */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
          className="cosmic-card"
        >
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">
              ⚙️ Microservices
            </h2>
            <div className="flex items-center space-x-2">
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${isAutoScaling ? 'text-secondary-600 bg-secondary-100' : 'text-gray-600 bg-gray-100'}`}>
                Auto-scaling {isAutoScaling ? 'ON' : 'OFF'}
              </span>
              <button
                onClick={triggerAutoScale}
                className="cosmic-button cosmic-button--outline text-xs py-1 px-2"
              >
                <ArrowTrendingUpIcon className="w-4 h-4 mr-1" />
                Scale
              </button>
            </div>
          </div>

          <div className="space-y-3 max-h-96 overflow-y-auto">
            {microservices.map((service) => (
              <div key={service.id} className="cosmic-card bg-gray-50">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    {getServiceTypeIcon(service.type)}
                    <span className="font-medium text-gray-900">{service.name}</span>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(service.status)}`}>
                    {service.status}
                  </span>
                </div>

                <div className="grid grid-cols-2 gap-2 text-sm mb-2">
                  <div>
                    <span className="text-gray-500">Instances:</span>
                    <span className="ml-1 font-medium">{service.instances}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Requests:</span>
                    <span className="ml-1 font-medium">{formatNumber(service.requests)}</span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-2 mb-2">
                  <div>
                    <div className="flex justify-between text-xs text-gray-600 mb-1">
                      <span>CPU</span>
                      <span>{service.cpu}%</span>
                    </div>
                    <div className="cosmic-progress">
                      <div 
                        className="cosmic-progress__bar"
                        style={{ width: `${service.cpu}%` }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between text-xs text-gray-600 mb-1">
                      <span>Memory</span>
                      <span>{service.memory}%</span>
                    </div>
                    <div className="cosmic-progress">
                      <div 
                        className="cosmic-progress__bar"
                        style={{ width: `${service.memory}%` }}
                      />
                    </div>
                  </div>
                </div>

                {service.errors > 0 && (
                  <div className="text-xs text-error-600">
                    ⚠️ {service.errors} errors in last hour
                  </div>
                )}
              </div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Scaling Events */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="cosmic-card mt-6"
      >
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          📈 Recent Scaling Events
        </h2>

        {scalingEvents.length > 0 ? (
          <div className="space-y-2">
            {scalingEvents.map((event) => (
              <div key={event.id} className="cosmic-card bg-gray-50">
                <div className="flex items-center justify-between">
                  <div>
                    <span className="font-medium text-gray-900">{event.description}</span>
                    <p className="text-sm text-gray-600">{event.action}</p>
                  </div>
                  <div className="text-right text-sm text-gray-500">
                    <div>{event.region}</div>
                    <div>{event.timestamp.toLocaleTimeString()}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <ArrowTrendingUpIcon className="w-12 h-12 mx-auto mb-3 text-gray-300" />
            <p>No scaling events yet</p>
            <p className="text-sm">System running optimally</p>
          </div>
        )}
      </motion.div>

      {/* Control Panel */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="cosmic-card mt-6"
      >
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          🎛️ Scalability Controls
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button
            onClick={triggerAutoScale}
            className="cosmic-button cosmic-button--primary"
          >
            <ArrowTrendingUpIcon className="w-5 h-5 mr-2" />
            Trigger Scale
          </button>
          
          <button
            onClick={() => setIsAutoScaling(!isAutoScaling)}
            className={`cosmic-button ${isAutoScaling ? 'cosmic-button--outline' : 'cosmic-button--secondary'}`}
          >
            <Cog6ToothIcon className="w-5 h-5 mr-2" />
            {isAutoScaling ? 'Disable' : 'Enable'} Auto-scale
          </button>
          
          <button className="cosmic-button cosmic-button--outline">
            <ChartBarIcon className="w-5 h-5 mr-2" />
            Performance Report
          </button>
          
          <button className="cosmic-button cosmic-button--outline">
            <BoltIcon className="w-5 h-5 mr-2" />
            Optimize Resources
          </button>
        </div>
      </motion.div>
    </div>
  );
};

export default InfiniteScalabilityCenter;
