import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ShieldCheckIcon,
  LockClosedIcon,
  KeyIcon,
  EyeIcon,
  EyeSlashIcon,
  CheckBadgeIcon,
  ExclamationTriangleIcon,
  CpuChipIcon,
  GlobeAltIcon,
  DocumentTextIcon,
  UserIcon,
} from '@heroicons/react/24/outline';

interface SecurityMetrics {
  encryptionLevel: string;
  authenticationFactors: number;
  complianceScore: number;
  threatLevel: 'low' | 'medium' | 'high' | 'critical';
  lastSecurityScan: Date;
  vulnerabilities: number;
  quantumResistance: number;
  privacyScore: number;
}

interface SecurityEvent {
  id: string;
  type: 'login' | 'data_access' | 'encryption' | 'audit' | 'threat_detected';
  severity: 'info' | 'warning' | 'error' | 'critical';
  timestamp: Date;
  description: string;
  location: string;
  resolved: boolean;
}

interface ComplianceStandard {
  id: string;
  name: string;
  status: 'compliant' | 'partial' | 'non_compliant';
  score: number;
  requirements: number;
  implemented: number;
  lastAudit: Date;
}

interface QuantumSecurityCenterProps {
  userId: string;
  onSecurityEvent?: (event: SecurityEvent) => void;
}

const QuantumSecurityCenter: React.FC<QuantumSecurityCenterProps> = ({
  userId,
  onSecurityEvent,
}) => {
  const [securityMetrics, setSecurityMetrics] = useState<SecurityMetrics>({
    encryptionLevel: 'AES-256-GCM + RSA-4096',
    authenticationFactors: 3,
    complianceScore: 98,
    threatLevel: 'low',
    lastSecurityScan: new Date(),
    vulnerabilities: 0,
    quantumResistance: 95,
    privacyScore: 97,
  });

  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [complianceStandards, setComplianceStandards] = useState<ComplianceStandard[]>([]);
  const [isScanning, setIsScanning] = useState(false);
  const [showSensitiveData, setShowSensitiveData] = useState(false);
  const [quantumShield, setQuantumShield] = useState(100);

  useEffect(() => {
    initializeSecurityCenter();
  }, []);

  useEffect(() => {
    // Quantum shield fluctuation
    const shieldInterval = setInterval(() => {
      setQuantumShield(prev => {
        const fluctuation = (Math.random() - 0.5) * 3;
        return Math.max(95, Math.min(100, prev + fluctuation));
      });
    }, 2000);

    return () => clearInterval(shieldInterval);
  }, []);

  const initializeSecurityCenter = async () => {
    // Initialize compliance standards
    const standards: ComplianceStandard[] = [
      {
        id: 'gdpr',
        name: 'GDPR',
        status: 'compliant',
        score: 98,
        requirements: 25,
        implemented: 25,
        lastAudit: new Date('2024-01-15'),
      },
      {
        id: 'hipaa',
        name: 'HIPAA',
        status: 'compliant',
        score: 96,
        requirements: 18,
        implemented: 18,
        lastAudit: new Date('2024-01-10'),
      },
      {
        id: 'fda',
        name: 'FDA 21 CFR Part 11',
        status: 'compliant',
        score: 94,
        requirements: 12,
        implemented: 12,
        lastAudit: new Date('2024-01-20'),
      },
      {
        id: 'ema',
        name: 'EMA GxP',
        status: 'partial',
        score: 89,
        requirements: 15,
        implemented: 14,
        lastAudit: new Date('2024-01-05'),
      },
      {
        id: 'iso27001',
        name: 'ISO 27001',
        status: 'compliant',
        score: 97,
        requirements: 114,
        implemented: 112,
        lastAudit: new Date('2024-01-25'),
      },
    ];

    setComplianceStandards(standards);

    // Generate sample security events
    const events: SecurityEvent[] = [
      {
        id: 'event-1',
        type: 'login',
        severity: 'info',
        timestamp: new Date(Date.now() - 5 * 60 * 1000),
        description: 'Successful multi-factor authentication',
        location: 'Warsaw, Poland',
        resolved: true,
      },
      {
        id: 'event-2',
        type: 'encryption',
        severity: 'info',
        timestamp: new Date(Date.now() - 15 * 60 * 1000),
        description: 'Quantum-resistant encryption applied to health data',
        location: 'Data Center EU-Central',
        resolved: true,
      },
      {
        id: 'event-3',
        type: 'audit',
        severity: 'info',
        timestamp: new Date(Date.now() - 30 * 60 * 1000),
        description: 'Automated compliance audit completed',
        location: 'Security Operations Center',
        resolved: true,
      },
    ];

    setSecurityEvents(events);
  };

  const performSecurityScan = useCallback(async () => {
    setIsScanning(true);
    
    try {
      // Simulate comprehensive security scan
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Update security metrics
      setSecurityMetrics(prev => ({
        ...prev,
        lastSecurityScan: new Date(),
        vulnerabilities: 0,
        quantumResistance: 95 + Math.random() * 5,
        privacyScore: 95 + Math.random() * 5,
      }));

      // Add scan event
      const scanEvent: SecurityEvent = {
        id: `scan-${Date.now()}`,
        type: 'audit',
        severity: 'info',
        timestamp: new Date(),
        description: 'Quantum security scan completed - No vulnerabilities detected',
        location: 'Security Operations Center',
        resolved: true,
      };

      setSecurityEvents(prev => [scanEvent, ...prev].slice(0, 10));
      onSecurityEvent?.(scanEvent);

    } catch (error) {
      console.error('Error performing security scan:', error);
    } finally {
      setIsScanning(false);
    }
  }, [onSecurityEvent]);

  const getThreatLevelColor = (level: string) => {
    switch (level) {
      case 'low':
        return 'text-secondary-600 bg-secondary-100';
      case 'medium':
        return 'text-warning-600 bg-warning-100';
      case 'high':
        return 'text-error-600 bg-error-100';
      case 'critical':
        return 'text-red-700 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getComplianceStatusColor = (status: string) => {
    switch (status) {
      case 'compliant':
        return 'text-secondary-600 bg-secondary-100';
      case 'partial':
        return 'text-warning-600 bg-warning-100';
      case 'non_compliant':
        return 'text-error-600 bg-error-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <ExclamationTriangleIcon className="w-4 h-4 text-red-600" />;
      case 'error':
        return <ExclamationTriangleIcon className="w-4 h-4 text-error-600" />;
      case 'warning':
        return <ExclamationTriangleIcon className="w-4 h-4 text-warning-600" />;
      case 'info':
        return <CheckBadgeIcon className="w-4 h-4 text-secondary-600" />;
      default:
        return <CheckBadgeIcon className="w-4 h-4 text-gray-600" />;
    }
  };

  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'login':
        return <UserIcon className="w-4 h-4" />;
      case 'data_access':
        return <DocumentTextIcon className="w-4 h-4" />;
      case 'encryption':
        return <LockClosedIcon className="w-4 h-4" />;
      case 'audit':
        return <CheckBadgeIcon className="w-4 h-4" />;
      case 'threat_detected':
        return <ExclamationTriangleIcon className="w-4 h-4" />;
      default:
        return <ShieldCheckIcon className="w-4 h-4" />;
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="cosmic-card cosmic-card--primary mb-6"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <motion.div
              animate={{ 
                rotate: 360,
                scale: [1, 1.1, 1],
              }}
              transition={{ 
                rotate: { duration: 12, repeat: Infinity, ease: 'linear' },
                scale: { duration: 4, repeat: Infinity, ease: 'easeInOut' }
              }}
              className="w-16 h-16 cosmic-gradient rounded-full flex items-center justify-center"
            >
              <ShieldCheckIcon className="w-8 h-8 text-white" />
            </motion.div>
            
            <div>
              <h1 className="text-3xl font-bold cosmic-gradient bg-clip-text text-transparent">
                🛡️ Quantum Security Center
              </h1>
              <p className="text-gray-600 mt-1">
                Zero-trust architecture with quantum-resistant encryption
              </p>
            </div>
          </div>

          {/* Security Status */}
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-secondary-600">
                {securityMetrics.complianceScore}%
              </div>
              <div className="text-xs text-gray-500">Compliance</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-primary-600">
                {securityMetrics.vulnerabilities}
              </div>
              <div className="text-xs text-gray-500">Vulnerabilities</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-warning-600">
                {Math.round(securityMetrics.quantumResistance)}%
              </div>
              <div className="text-xs text-gray-500">Quantum Shield</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-600">
                {securityMetrics.authenticationFactors}FA
              </div>
              <div className="text-xs text-gray-500">Auth Factors</div>
            </div>
          </div>
        </div>

        {/* Quantum Shield Status */}
        <div className="mt-4">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
            <span>Quantum Shield Protection</span>
            <span>{Math.round(quantumShield)}% Active</span>
          </div>
          <div className="cosmic-progress">
            <motion.div 
              className="cosmic-progress__bar"
              animate={{ width: `${quantumShield}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
        </div>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Security Metrics */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1 }}
          className="cosmic-card"
        >
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            🔒 Security Metrics
          </h2>

          <div className="space-y-4">
            {/* Encryption Level */}
            <div className="cosmic-card bg-gray-50">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <LockClosedIcon className="w-5 h-5 text-primary-600" />
                  <span className="font-medium">Encryption</span>
                </div>
                <CheckBadgeIcon className="w-5 h-5 text-secondary-600" />
              </div>
              <p className="text-sm text-gray-600">{securityMetrics.encryptionLevel}</p>
            </div>

            {/* Threat Level */}
            <div className="cosmic-card bg-gray-50">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <ExclamationTriangleIcon className="w-5 h-5 text-warning-600" />
                  <span className="font-medium">Threat Level</span>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${getThreatLevelColor(securityMetrics.threatLevel)}`}>
                  {securityMetrics.threatLevel}
                </span>
              </div>
              <p className="text-sm text-gray-600">No active threats detected</p>
            </div>

            {/* Privacy Score */}
            <div className="cosmic-card bg-gray-50">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <EyeSlashIcon className="w-5 h-5 text-purple-600" />
                  <span className="font-medium">Privacy Protection</span>
                </div>
                <span className="text-lg font-bold text-purple-600">
                  {Math.round(securityMetrics.privacyScore)}%
                </span>
              </div>
              <div className="cosmic-progress">
                <div 
                  className="cosmic-progress__bar"
                  style={{ width: `${securityMetrics.privacyScore}%` }}
                />
              </div>
            </div>

            {/* Last Security Scan */}
            <div className="cosmic-card bg-gray-50">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <CpuChipIcon className="w-5 h-5 text-indigo-600" />
                  <span className="font-medium">Last Scan</span>
                </div>
                <button
                  onClick={performSecurityScan}
                  disabled={isScanning}
                  className="cosmic-button cosmic-button--outline text-xs py-1 px-2"
                >
                  {isScanning ? 'Scanning...' : 'Scan Now'}
                </button>
              </div>
              <p className="text-sm text-gray-600">
                {securityMetrics.lastSecurityScan.toLocaleString()}
              </p>
            </div>
          </div>
        </motion.div>

        {/* Compliance Standards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="cosmic-card"
        >
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            📋 Compliance Standards
          </h2>

          <div className="space-y-3">
            {complianceStandards.map((standard) => (
              <div key={standard.id} className="cosmic-card cosmic-card--health">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-gray-900">{standard.name}</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getComplianceStatusColor(standard.status)}`}>
                    {standard.status.replace('_', ' ')}
                  </span>
                </div>
                
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600">
                    {standard.implemented}/{standard.requirements} requirements
                  </span>
                  <span className="text-lg font-bold text-secondary-600">
                    {standard.score}%
                  </span>
                </div>
                
                <div className="cosmic-progress">
                  <div 
                    className="cosmic-progress__bar"
                    style={{ width: `${(standard.implemented / standard.requirements) * 100}%` }}
                  />
                </div>
                
                <p className="text-xs text-gray-500 mt-2">
                  Last audit: {standard.lastAudit.toLocaleDateString()}
                </p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Security Events */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
          className="cosmic-card"
        >
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">
              🔍 Security Events
            </h2>
            <button
              onClick={() => setShowSensitiveData(!showSensitiveData)}
              className="cosmic-button cosmic-button--outline text-xs py-1 px-2"
            >
              {showSensitiveData ? <EyeSlashIcon className="w-4 h-4" /> : <EyeIcon className="w-4 h-4" />}
            </button>
          </div>

          <div className="space-y-3 max-h-96 overflow-y-auto">
            <AnimatePresence>
              {securityEvents.map((event, index) => (
                <motion.div
                  key={event.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ delay: index * 0.05 }}
                  className="cosmic-card bg-gray-50"
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {getEventTypeIcon(event.type)}
                      <span className="font-medium text-gray-900 capitalize">
                        {event.type.replace('_', ' ')}
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      {getSeverityIcon(event.severity)}
                      <span className="text-xs text-gray-500">
                        {event.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-2">{event.description}</p>
                  
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>📍 {showSensitiveData ? event.location : '***'}</span>
                    {event.resolved && (
                      <span className="flex items-center space-x-1 text-secondary-600">
                        <CheckBadgeIcon className="w-3 h-3" />
                        <span>Resolved</span>
                      </span>
                    )}
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>

          {securityEvents.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <ShieldCheckIcon className="w-12 h-12 mx-auto mb-3 text-gray-300" />
              <p>No security events</p>
              <p className="text-sm">All systems secure</p>
            </div>
          )}
        </motion.div>
      </div>

      {/* Security Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="cosmic-card mt-6"
      >
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          ⚡ Security Actions
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button
            onClick={performSecurityScan}
            disabled={isScanning}
            className="cosmic-button cosmic-button--primary"
          >
            <CpuChipIcon className="w-5 h-5 mr-2" />
            {isScanning ? 'Scanning...' : 'Security Scan'}
          </button>
          
          <button className="cosmic-button cosmic-button--outline">
            <KeyIcon className="w-5 h-5 mr-2" />
            Rotate Keys
          </button>
          
          <button className="cosmic-button cosmic-button--outline">
            <DocumentTextIcon className="w-5 h-5 mr-2" />
            Audit Report
          </button>
          
          <button className="cosmic-button cosmic-button--outline">
            <GlobeAltIcon className="w-5 h-5 mr-2" />
            Threat Intel
          </button>
        </div>
      </motion.div>
    </div>
  );
};

export default QuantumSecurityCenter;
