import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  SparklesIcon,
  BeakerIcon,
  HeartIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  LightBulbIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  BoltIcon,
  StarIcon,
} from '@heroicons/react/24/outline';
import CosmicKnowledgeGraph from '../graph/CosmicKnowledgeGraph';
import CosmicDashboard from '../dashboard/CosmicDashboard';
import HealthProfileManager from '../profile/HealthProfileManager';
import '../../styles/cosmic-design-system.css';

interface InterfaceMode {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  description: string;
  color: string;
  gradient: string;
}

interface CosmicHealthInterfaceProps {
  userId: string;
}

const CosmicHealthInterface: React.FC<CosmicHealthInterfaceProps> = ({ userId }) => {
  const [activeMode, setActiveMode] = useState('dashboard');
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [cosmicEnergy, setCosmicEnergy] = useState(100);

  // Interface modes with cosmic power
  const interfaceModes: InterfaceMode[] = [
    {
      id: 'dashboard',
      name: 'Cosmic Dashboard',
      icon: SparklesIcon,
      description: 'Your personalized health universe overview',
      color: 'text-primary-600',
      gradient: 'from-primary-500 to-secondary-500',
    },
    {
      id: 'profile',
      name: 'Health Profile',
      icon: HeartIcon,
      description: 'Comprehensive health data management',
      color: 'text-secondary-600',
      gradient: 'from-secondary-500 to-primary-500',
    },
    {
      id: 'graph',
      name: 'Knowledge Graph',
      icon: BeakerIcon,
      description: 'Interactive supplement universe exploration',
      color: 'text-warning-600',
      gradient: 'from-warning-500 to-error-500',
    },
    {
      id: 'analysis',
      name: 'AI Analysis',
      icon: BoltIcon,
      description: 'Cosmic-level AI-powered insights',
      color: 'text-purple-600',
      gradient: 'from-purple-500 to-pink-500',
    },
    {
      id: 'recommendations',
      name: 'Smart Recommendations',
      icon: LightBulbIcon,
      description: 'Personalized supplement optimization',
      color: 'text-indigo-600',
      gradient: 'from-indigo-500 to-blue-500',
    },
  ];

  useEffect(() => {
    initializeInterface();
  }, []);

  const initializeInterface = async () => {
    try {
      setIsLoading(true);
      
      // Simulate cosmic initialization
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Animate cosmic energy
      const energyInterval = setInterval(() => {
        setCosmicEnergy(prev => {
          const newEnergy = prev + (Math.random() - 0.5) * 10;
          return Math.max(80, Math.min(100, newEnergy));
        });
      }, 1000);

      setIsLoading(false);
      
      return () => clearInterval(energyInterval);
    } catch (error) {
      console.error('Error initializing cosmic interface:', error);
      setIsLoading(false);
    }
  };

  const handleModeChange = useCallback((modeId: string) => {
    setActiveMode(modeId);
  }, []);

  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    // Implement cosmic search logic
  }, []);

  const renderActiveInterface = () => {
    switch (activeMode) {
      case 'dashboard':
        return <CosmicDashboard userId={userId} />;
      case 'profile':
        return <HealthProfileManager userId={userId} />;
      case 'graph':
        return (
          <div className="p-6">
            <CosmicKnowledgeGraph
              data={{ nodes: [], links: [] }}
              width={1200}
              height={800}
            />
          </div>
        );
      case 'analysis':
        return <CosmicAIAnalysis userId={userId} />;
      case 'recommendations':
        return <CosmicRecommendations userId={userId} />;
      default:
        return <CosmicDashboard userId={userId} />;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 via-secondary-50 to-warning-50 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <div className="cosmic-health-score cosmic-pulse mb-6" style={{ width: '120px', height: '120px' }}>
            <div className="cosmic-health-score__value text-2xl">
              🌟
            </div>
          </div>
          <h2 className="text-3xl font-bold cosmic-gradient bg-clip-text text-transparent mb-4">
            Initializing Cosmic Health Platform
          </h2>
          <p className="text-gray-600 text-lg mb-6">
            Preparing your personalized health universe...
          </p>
          <div className="cosmic-progress w-64 mx-auto">
            <motion.div 
              className="cosmic-progress__bar"
              initial={{ width: 0 }}
              animate={{ width: '100%' }}
              transition={{ duration: 2, ease: 'easeInOut' }}
            />
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-primary-50 to-secondary-50">
      {/* Cosmic Header */}
      <motion.header
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white/80 backdrop-blur-lg cosmic-shadow border-b sticky top-0 z-50"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo and Title */}
            <div className="flex items-center space-x-4">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 20, repeat: Infinity, ease: 'linear' }}
                className="w-10 h-10 cosmic-gradient rounded-full flex items-center justify-center"
              >
                <SparklesIcon className="w-6 h-6 text-white" />
              </motion.div>
              <div>
                <h1 className="text-xl font-bold cosmic-gradient bg-clip-text text-transparent">
                  Suplementor Cosmic Health Platform
                </h1>
                <p className="text-xs text-gray-500">
                  Powered by AI • Golden Ratio Design • Cosmic Energy: {Math.round(cosmicEnergy)}%
                </p>
              </div>
            </div>

            {/* Cosmic Search */}
            <div className="flex-1 max-w-lg mx-8">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search the health universe..."
                  className="cosmic-input pl-10 pr-12"
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                />
                <button
                  onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-primary-600 transition-colors"
                >
                  <AdjustmentsHorizontalIcon className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Cosmic Energy Indicator */}
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <div className="text-sm font-medium text-gray-900">Cosmic Energy</div>
                <div className="cosmic-progress w-20">
                  <motion.div 
                    className="cosmic-progress__bar"
                    animate={{ width: `${cosmicEnergy}%` }}
                    transition={{ duration: 0.5 }}
                  />
                </div>
              </div>
              <div className="cosmic-badge cosmic-badge--success">
                AI Active
              </div>
            </div>
          </div>
        </div>
      </motion.header>

      {/* Interface Mode Selector */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-white/60 backdrop-blur border-b"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-1 py-4 overflow-x-auto">
            {interfaceModes.map((mode, index) => (
              <motion.button
                key={mode.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                onClick={() => handleModeChange(mode.id)}
                className={`flex items-center space-x-3 px-6 py-3 rounded-xl font-medium transition-all whitespace-nowrap ${
                  activeMode === mode.id
                    ? `bg-gradient-to-r ${mode.gradient} text-white cosmic-shadow-lg transform scale-105`
                    : 'bg-white/50 text-gray-600 hover:bg-white/80 hover:text-gray-900'
                }`}
              >
                <mode.icon className="w-5 h-5" />
                <span>{mode.name}</span>
                {activeMode === mode.id && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="w-2 h-2 bg-white rounded-full"
                  />
                )}
              </motion.button>
            ))}
          </div>
        </div>
      </motion.div>

      {/* Advanced Filters Panel */}
      <AnimatePresence>
        {showAdvancedFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-white/90 backdrop-blur border-b"
          >
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
              <CosmicAdvancedFilters />
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Interface Content */}
      <main className="relative">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeMode}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            {renderActiveInterface()}
          </motion.div>
        </AnimatePresence>
      </main>

      {/* Cosmic Status Bar */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="fixed bottom-4 right-4 cosmic-card bg-white/90 backdrop-blur"
      >
        <div className="flex items-center space-x-3 text-sm">
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-secondary-500 rounded-full cosmic-pulse"></div>
            <span className="text-gray-600">System Active</span>
          </div>
          <div className="text-gray-400">|</div>
          <div className="flex items-center space-x-1">
            <BoltIcon className="w-4 h-4 text-warning-500" />
            <span className="text-gray-600">AI Processing</span>
          </div>
          <div className="text-gray-400">|</div>
          <div className="flex items-center space-x-1">
            <StarIcon className="w-4 h-4 text-primary-500" />
            <span className="text-gray-600">Cosmic Mode</span>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

// Cosmic AI Analysis Component
const CosmicAIAnalysis: React.FC<{ userId: string }> = ({ userId }) => (
  <div className="p-6">
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="cosmic-card cosmic-card--primary"
    >
      <h2 className="text-2xl font-bold text-gray-900 mb-4">🧠 Cosmic AI Analysis</h2>
      <p className="text-gray-600 mb-6">
        Advanced AI-powered health insights using cosmic-level algorithms
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="cosmic-card">
          <h3 className="font-semibold text-gray-900 mb-2">Risk Assessment</h3>
          <div className="cosmic-health-score" style={{ width: '60px', height: '60px' }}>
            <div className="cosmic-health-score__value" style={{ fontSize: '14px' }}>23</div>
          </div>
          <p className="text-sm text-gray-600 mt-2">Low risk detected</p>
        </div>
        <div className="cosmic-card">
          <h3 className="font-semibold text-gray-900 mb-2">Optimization Score</h3>
          <div className="cosmic-health-score" style={{ width: '60px', height: '60px' }}>
            <div className="cosmic-health-score__value" style={{ fontSize: '14px' }}>87</div>
          </div>
          <p className="text-sm text-gray-600 mt-2">Excellent optimization</p>
        </div>
        <div className="cosmic-card">
          <h3 className="font-semibold text-gray-900 mb-2">AI Confidence</h3>
          <div className="cosmic-health-score" style={{ width: '60px', height: '60px' }}>
            <div className="cosmic-health-score__value" style={{ fontSize: '14px' }}>94</div>
          </div>
          <p className="text-sm text-gray-600 mt-2">Very high confidence</p>
        </div>
      </div>
    </motion.div>
  </div>
);

// Cosmic Recommendations Component
const CosmicRecommendations: React.FC<{ userId: string }> = ({ userId }) => (
  <div className="p-6">
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="cosmic-card cosmic-card--health"
    >
      <h2 className="text-2xl font-bold text-gray-900 mb-4">💡 Cosmic Recommendations</h2>
      <p className="text-gray-600 mb-6">
        Personalized supplement recommendations optimized with golden ratio algorithms
      </p>
      <div className="space-y-4">
        {[1, 2, 3].map((item) => (
          <div key={item} className="cosmic-card flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 cosmic-gradient rounded-full flex items-center justify-center">
                <BeakerIcon className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Recommended Supplement {item}</h3>
                <p className="text-sm text-gray-600">Optimized for your health goals</p>
              </div>
            </div>
            <button className="cosmic-button cosmic-button--primary">
              Add to Stack
            </button>
          </div>
        ))}
      </div>
    </motion.div>
  </div>
);

// Cosmic Advanced Filters Component
const CosmicAdvancedFilters: React.FC = () => (
  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
      <select className="cosmic-input">
        <option>All Categories</option>
        <option>Vitamins</option>
        <option>Minerals</option>
        <option>Herbs</option>
      </select>
    </div>
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">Safety Level</label>
      <select className="cosmic-input">
        <option>All Levels</option>
        <option>High Safety</option>
        <option>Moderate Safety</option>
      </select>
    </div>
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">Evidence Level</label>
      <select className="cosmic-input">
        <option>All Evidence</option>
        <option>Strong Evidence</option>
        <option>Moderate Evidence</option>
      </select>
    </div>
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">Price Range</label>
      <select className="cosmic-input">
        <option>All Prices</option>
        <option>$0 - $25</option>
        <option>$25 - $50</option>
        <option>$50+</option>
      </select>
    </div>
  </div>
);

export default CosmicHealthInterface;
