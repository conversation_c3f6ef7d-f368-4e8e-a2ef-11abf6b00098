import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  DevicePhoneMobileIcon,
  HeartIcon,
  BoltIcon,
  MoonIcon,
  FireIcon,
  ClockIcon,
  SignalIcon,
  WifiIcon,
  Battery50Icon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

interface WearableDevice {
  id: string;
  name: string;
  type: 'smartwatch' | 'fitness_tracker' | 'smart_ring' | 'continuous_monitor' | 'smart_scale';
  brand: string;
  model: string;
  status: 'connected' | 'syncing' | 'disconnected' | 'low_battery';
  batteryLevel: number;
  lastSync: Date;
  dataTypes: string[];
  accuracy: number;
}

interface BiometricReading {
  id: string;
  deviceId: string;
  type: 'heart_rate' | 'steps' | 'sleep' | 'calories' | 'weight' | 'blood_oxygen' | 'stress' | 'hrv';
  value: number;
  unit: string;
  timestamp: Date;
  quality: 'excellent' | 'good' | 'fair' | 'poor';
  trend: 'up' | 'down' | 'stable';
}

interface HealthInsight {
  id: string;
  type: 'achievement' | 'warning' | 'recommendation' | 'trend';
  title: string;
  description: string;
  severity: 'info' | 'warning' | 'success' | 'error';
  timestamp: Date;
  deviceSource: string;
  actionable: boolean;
}

interface CosmicWearableHubProps {
  userId: string;
  onNewReading?: (reading: BiometricReading) => void;
  onInsightGenerated?: (insight: HealthInsight) => void;
}

const CosmicWearableHub: React.FC<CosmicWearableHubProps> = ({
  userId,
}) => {
  const [devices, setDevices] = useState<WearableDevice[]>([]);
  const [recentReadings, setRecentReadings] = useState<BiometricReading[]>([]);
  const [insights, setInsights] = useState<HealthInsight[]>([]);
  const [isScanning, setIsScanning] = useState(false);
  const [syncProgress, setSyncProgress] = useState(0);
  const [cosmicSync, setCosmicSync] = useState(true);

  useEffect(() => {
    initializeWearableHub();
  }, []);

  useEffect(() => {
    if (cosmicSync) {
      const syncInterval = setInterval(() => {
        syncAllDevices();
      }, 30000); // Sync every 30 seconds

      return () => clearInterval(syncInterval);
    }
  }, [cosmicSync]);

  const initializeWearableHub = () => {
    // Initialize sample devices
    const sampleDevices: WearableDevice[] = [
      {
        id: 'apple-watch-1',
        name: 'Apple Watch Series 9',
        type: 'smartwatch',
        brand: 'Apple',
        model: 'Series 9',
        status: 'connected',
        batteryLevel: 87,
        lastSync: new Date(Date.now() - 5 * 60 * 1000),
        dataTypes: ['heart_rate', 'steps', 'calories', 'sleep', 'blood_oxygen', 'hrv'],
        accuracy: 95,
      },
      {
        id: 'oura-ring-1',
        name: 'Oura Ring Gen3',
        type: 'smart_ring',
        brand: 'Oura',
        model: 'Generation 3',
        status: 'connected',
        batteryLevel: 72,
        lastSync: new Date(Date.now() - 10 * 60 * 1000),
        dataTypes: ['sleep', 'heart_rate', 'hrv', 'stress'],
        accuracy: 92,
      },
      {
        id: 'withings-scale-1',
        name: 'Withings Body+',
        type: 'smart_scale',
        brand: 'Withings',
        model: 'Body+',
        status: 'connected',
        batteryLevel: 45,
        lastSync: new Date(Date.now() - 2 * 60 * 60 * 1000),
        dataTypes: ['weight', 'body_fat', 'muscle_mass'],
        accuracy: 98,
      },
      {
        id: 'dexcom-g7-1',
        name: 'Dexcom G7 CGM',
        type: 'continuous_monitor',
        brand: 'Dexcom',
        model: 'G7',
        status: 'syncing',
        batteryLevel: 91,
        lastSync: new Date(Date.now() - 1 * 60 * 1000),
        dataTypes: ['glucose'],
        accuracy: 99,
      },
    ];

    setDevices(sampleDevices);

    // Generate sample readings
    generateSampleReadings(sampleDevices);
    
    // Generate sample insights
    generateSampleInsights();
  };

  const generateSampleReadings = (deviceList: WearableDevice[]) => {
    const readings: BiometricReading[] = [];
    const now = new Date();

    deviceList.forEach(device => {
      device.dataTypes.forEach(dataType => {
        const reading: BiometricReading = {
          id: `reading-${device.id}-${dataType}-${Date.now()}`,
          deviceId: device.id,
          type: dataType as any,
          value: generateSampleValue(dataType),
          unit: getUnitForDataType(dataType),
          timestamp: new Date(now.getTime() - Math.random() * 60 * 60 * 1000),
          quality: ['excellent', 'good', 'fair'][Math.floor(Math.random() * 3)] as any,
          trend: ['up', 'down', 'stable'][Math.floor(Math.random() * 3)] as any,
        };
        readings.push(reading);
      });
    });

    setRecentReadings(readings.slice(0, 20));
  };

  const generateSampleValue = (dataType: string): number => {
    switch (dataType) {
      case 'heart_rate':
        return 60 + Math.random() * 40;
      case 'steps':
        return Math.floor(Math.random() * 15000);
      case 'calories':
        return Math.floor(Math.random() * 3000);
      case 'sleep':
        return 6 + Math.random() * 3;
      case 'weight':
        return 60 + Math.random() * 40;
      case 'blood_oxygen':
        return 95 + Math.random() * 5;
      case 'stress':
        return Math.random() * 100;
      case 'hrv':
        return 20 + Math.random() * 60;
      case 'glucose':
        return 80 + Math.random() * 60;
      default:
        return Math.random() * 100;
    }
  };

  const getUnitForDataType = (dataType: string): string => {
    switch (dataType) {
      case 'heart_rate':
        return 'bpm';
      case 'steps':
        return 'steps';
      case 'calories':
        return 'kcal';
      case 'sleep':
        return 'hours';
      case 'weight':
        return 'kg';
      case 'blood_oxygen':
        return '%';
      case 'stress':
        return 'score';
      case 'hrv':
        return 'ms';
      case 'glucose':
        return 'mg/dL';
      default:
        return 'unit';
    }
  };

  const generateSampleInsights = () => {
    const sampleInsights: HealthInsight[] = [
      {
        id: 'insight-1',
        type: 'achievement',
        title: 'Sleep Goal Achieved!',
        description: 'You\'ve maintained 8+ hours of quality sleep for 7 consecutive days.',
        severity: 'success',
        timestamp: new Date(Date.now() - 30 * 60 * 1000),
        deviceSource: 'Oura Ring Gen3',
        actionable: false,
      },
      {
        id: 'insight-2',
        type: 'recommendation',
        title: 'Optimize Supplement Timing',
        description: 'Your HRV data suggests taking Magnesium 2 hours earlier for better sleep quality.',
        severity: 'info',
        timestamp: new Date(Date.now() - 60 * 60 * 1000),
        deviceSource: 'Apple Watch Series 9',
        actionable: true,
      },
      {
        id: 'insight-3',
        type: 'warning',
        title: 'Elevated Stress Detected',
        description: 'Your stress levels have been consistently high. Consider meditation or breathing exercises.',
        severity: 'warning',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        deviceSource: 'Apple Watch Series 9',
        actionable: true,
      },
    ];

    setInsights(sampleInsights);
  };

  const syncAllDevices = useCallback(async () => {
    if (isScanning) return;

    setIsScanning(true);
    setSyncProgress(0);

    try {
      for (let i = 0; i < devices.length; i++) {
        // Simulate sync for each device
        await new Promise(resolve => setTimeout(resolve, 500));
        setSyncProgress(((i + 1) / devices.length) * 100);
        
        // Update device status
        setDevices(prev => prev.map(device => ({
          ...device,
          lastSync: new Date(),
          status: device.status === 'disconnected' ? 'connected' : device.status,
        })));
      }

      // Generate new readings
      generateSampleReadings(devices);

    } catch (error) {
      console.error('Error syncing devices:', error);
    } finally {
      setIsScanning(false);
      setSyncProgress(0);
    }
  }, [devices, isScanning]);

  const getDeviceIcon = (type: string) => {
    switch (type) {
      case 'smartwatch':
        return '⌚';
      case 'fitness_tracker':
        return '📱';
      case 'smart_ring':
        return '💍';
      case 'continuous_monitor':
        return '🩺';
      case 'smart_scale':
        return '⚖️';
      default:
        return '📱';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
        return 'text-secondary-600 bg-secondary-100';
      case 'syncing':
        return 'text-warning-600 bg-warning-100';
      case 'disconnected':
        return 'text-error-600 bg-error-100';
      case 'low_battery':
        return 'text-orange-600 bg-orange-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getDataTypeIcon = (type: string) => {
    switch (type) {
      case 'heart_rate':
        return <HeartIcon className="w-4 h-4" />;
      case 'steps':
        return <BoltIcon className="w-4 h-4" />;
      case 'sleep':
        return <MoonIcon className="w-4 h-4" />;
      case 'calories':
        return <FireIcon className="w-4 h-4" />;
      default:
        return <SignalIcon className="w-4 h-4" />;
    }
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'achievement':
        return <CheckCircleIcon className="w-5 h-5 text-secondary-600" />;
      case 'warning':
        return <ExclamationTriangleIcon className="w-5 h-5 text-warning-600" />;
      case 'recommendation':
        return <BoltIcon className="w-5 h-5 text-primary-600" />;
      case 'trend':
        return <SignalIcon className="w-5 h-5 text-indigo-600" />;
      default:
        return <SignalIcon className="w-5 h-5 text-gray-600" />;
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="cosmic-card cosmic-card--primary mb-6"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <motion.div
              animate={{ 
                rotate: 360,
                scale: [1, 1.1, 1],
              }}
              transition={{ 
                rotate: { duration: 8, repeat: Infinity, ease: 'linear' },
                scale: { duration: 3, repeat: Infinity, ease: 'easeInOut' }
              }}
              className="w-16 h-16 cosmic-gradient rounded-full flex items-center justify-center"
            >
              <DevicePhoneMobileIcon className="w-8 h-8 text-white" />
            </motion.div>
            
            <div>
              <h1 className="text-3xl font-bold cosmic-gradient bg-clip-text text-transparent">
                📱 Cosmic Wearable Hub
              </h1>
              <p className="text-gray-600 mt-1">
                Real-time biometric integration from 50+ device types
              </p>
            </div>
          </div>

          {/* Sync Controls */}
          <div className="flex items-center space-x-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-secondary-600">
                {devices.filter(d => d.status === 'connected').length}
              </div>
              <div className="text-xs text-gray-500">Connected</div>
            </div>
            
            <button
              onClick={syncAllDevices}
              disabled={isScanning}
              className="cosmic-button cosmic-button--primary"
            >
              <WifiIcon className="w-5 h-5 mr-2" />
              {isScanning ? 'Syncing...' : 'Sync All'}
            </button>
            
            <button
              onClick={() => setCosmicSync(!cosmicSync)}
              className={`cosmic-button ${cosmicSync ? 'cosmic-button--secondary' : 'cosmic-button--outline'}`}
            >
              <BoltIcon className="w-5 h-5 mr-2" />
              Auto-Sync {cosmicSync ? 'ON' : 'OFF'}
            </button>
          </div>
        </div>

        {/* Sync Progress */}
        {isScanning && (
          <div className="mt-4">
            <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
              <span>Syncing devices...</span>
              <span>{Math.round(syncProgress)}%</span>
            </div>
            <div className="cosmic-progress">
              <motion.div 
                className="cosmic-progress__bar"
                animate={{ width: `${syncProgress}%` }}
                transition={{ duration: 0.3 }}
              />
            </div>
          </div>
        )}
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Connected Devices */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1 }}
          className="cosmic-card"
        >
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            📱 Connected Devices
          </h2>

          <div className="space-y-3">
            {devices.map((device) => (
              <div key={device.id} className="cosmic-card cosmic-card--health">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{getDeviceIcon(device.type)}</span>
                    <div>
                      <span className="font-medium text-gray-900">{device.name}</span>
                      <p className="text-sm text-gray-600">{device.brand} {device.model}</p>
                    </div>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(device.status)}`}>
                    {device.status.replace('_', ' ')}
                  </span>
                </div>

                <div className="grid grid-cols-2 gap-2 text-sm mb-2">
                  <div className="flex items-center space-x-1">
                    <Battery50Icon className="w-4 h-4 text-gray-500" />
                    <span>{device.batteryLevel}%</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <ClockIcon className="w-4 h-4 text-gray-500" />
                    <span>{device.lastSync.toLocaleTimeString()}</span>
                  </div>
                </div>

                <div className="flex flex-wrap gap-1">
                  {device.dataTypes.slice(0, 3).map(dataType => (
                    <span key={dataType} className="cosmic-badge cosmic-badge--primary text-xs">
                      {dataType.replace('_', ' ')}
                    </span>
                  ))}
                  {device.dataTypes.length > 3 && (
                    <span className="cosmic-badge cosmic-badge--success text-xs">
                      +{device.dataTypes.length - 3} more
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Recent Readings */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="cosmic-card"
        >
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            📊 Recent Readings
          </h2>

          <div className="space-y-2 max-h-96 overflow-y-auto">
            {recentReadings.slice(0, 10).map((reading) => (
              <div key={reading.id} className="cosmic-card bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {getDataTypeIcon(reading.type)}
                    <div>
                      <span className="font-medium text-gray-900">
                        {reading.value.toFixed(1)} {reading.unit}
                      </span>
                      <p className="text-xs text-gray-600 capitalize">
                        {reading.type.replace('_', ' ')}
                      </p>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-xs text-gray-500">
                      {reading.timestamp.toLocaleTimeString()}
                    </div>
                    <div className={`text-xs font-medium ${
                      reading.quality === 'excellent' ? 'text-secondary-600' :
                      reading.quality === 'good' ? 'text-primary-600' :
                      reading.quality === 'fair' ? 'text-warning-600' :
                      'text-error-600'
                    }`}>
                      {reading.quality}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Health Insights */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
          className="cosmic-card"
        >
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            💡 Health Insights
          </h2>

          <div className="space-y-3">
            {insights.map((insight) => (
              <div key={insight.id} className="cosmic-card cosmic-card--health">
                <div className="flex items-start space-x-3">
                  {getInsightIcon(insight.type)}
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900 mb-1">{insight.title}</h3>
                    <p className="text-sm text-gray-600 mb-2">{insight.description}</p>
                    
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>📱 {insight.deviceSource}</span>
                      <span>{insight.timestamp.toLocaleTimeString()}</span>
                    </div>
                    
                    {insight.actionable && (
                      <button className="cosmic-button cosmic-button--outline text-xs py-1 px-2 mt-2">
                        Take Action
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default CosmicWearableHub;
