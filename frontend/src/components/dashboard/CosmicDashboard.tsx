import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  HeartIcon,
  ChartBarIcon,
  BeakerIcon,
  ShieldCheckIcon,
  LightBulbIcon,
  ArrowTrendingUpIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import '../../styles/cosmic-design-system.css';

interface HealthMetrics {
  healthScore: number;
  riskScore: number;
  goalProgress: number;
  supplementCount: number;
  lastUpdated: Date;
}

interface DashboardStats {
  totalSupplements: number;
  activeGoals: number;
  completedGoals: number;
  safetyAlerts: number;
  monthlyBudget: number;
  budgetUsed: number;
}

interface RecentActivity {
  id: string;
  type: 'goal_achieved' | 'supplement_added' | 'health_data' | 'interaction_alert';
  title: string;
  description: string;
  timestamp: Date;
  severity?: 'info' | 'warning' | 'success' | 'error';
}

interface CosmicDashboardProps {
  userId: string;
}

const CosmicDashboard: React.FC<CosmicDashboardProps> = ({ userId }) => {
  const [healthMetrics, setHealthMetrics] = useState<HealthMetrics>({
    healthScore: 0,
    riskScore: 0,
    goalProgress: 0,
    supplementCount: 0,
    lastUpdated: new Date(),
  });

  const [dashboardStats, setDashboardStats] = useState<DashboardStats>({
    totalSupplements: 0,
    activeGoals: 0,
    completedGoals: 0,
    safetyAlerts: 0,
    monthlyBudget: 0,
    budgetUsed: 0,
  });

  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, [userId]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Simulate API calls - replace with actual API calls
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setHealthMetrics({
        healthScore: 87,
        riskScore: 23,
        goalProgress: 68,
        supplementCount: 5,
        lastUpdated: new Date(),
      });

      setDashboardStats({
        totalSupplements: 5,
        activeGoals: 4,
        completedGoals: 2,
        safetyAlerts: 1,
        monthlyBudget: 150,
        budgetUsed: 89,
      });

      setRecentActivity([
        {
          id: '1',
          type: 'goal_achieved',
          title: 'Sleep Quality Goal Achieved',
          description: 'You\'ve maintained 8+ hours of sleep for 7 consecutive days!',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
          severity: 'success',
        },
        {
          id: '2',
          type: 'supplement_added',
          title: 'New Supplement Added',
          description: 'Magnesium Glycinate added to your stack',
          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
          severity: 'info',
        },
        {
          id: '3',
          type: 'interaction_alert',
          title: 'Mild Interaction Detected',
          description: 'Potential timing conflict between Vitamin D and Magnesium',
          timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000),
          severity: 'warning',
        },
      ]);

    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'var(--secondary-500)';
    if (score >= 60) return 'var(--warning-500)';
    return 'var(--error-500)';
  };

  const getRiskScoreColor = (score: number) => {
    if (score <= 30) return 'var(--secondary-500)';
    if (score <= 60) return 'var(--warning-500)';
    return 'var(--error-500)';
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'goal_achieved':
        return <CheckCircleIcon className="w-5 h-5" />;
      case 'supplement_added':
        return <BeakerIcon className="w-5 h-5" />;
      case 'health_data':
        return <ChartBarIcon className="w-5 h-5" />;
      case 'interaction_alert':
        return <ExclamationTriangleIcon className="w-5 h-5" />;
      default:
        return <LightBulbIcon className="w-5 h-5" />;
    }
  };

  const getSeverityColor = (severity?: string) => {
    switch (severity) {
      case 'success':
        return 'var(--secondary-500)';
      case 'warning':
        return 'var(--warning-500)';
      case 'error':
        return 'var(--error-500)';
      default:
        return 'var(--primary-500)';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="cosmic-spin w-12 h-12 border-4 border-primary-200 border-t-primary-600 rounded-full"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Welcome to Your Health Universe 🌟
          </h1>
          <p className="text-gray-600 text-lg">
            Your personalized health insights powered by cosmic-level AI
          </p>
        </motion.div>

        {/* Health Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="cosmic-card cosmic-card--health"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Health Score</p>
                <p className="text-3xl font-bold" style={{ color: getHealthScoreColor(healthMetrics.healthScore) }}>
                  {healthMetrics.healthScore}
                </p>
                <p className="text-xs text-gray-500">out of 100</p>
              </div>
              <div className="cosmic-health-score" style={{ width: '60px', height: '60px' }}>
                <div className="cosmic-health-score__value" style={{ fontSize: '14px' }}>
                  {healthMetrics.healthScore}
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="cosmic-card"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Risk Score</p>
                <p className="text-3xl font-bold" style={{ color: getRiskScoreColor(healthMetrics.riskScore) }}>
                  {healthMetrics.riskScore}
                </p>
                <p className="text-xs text-gray-500">cosmic scale</p>
              </div>
              <ShieldCheckIcon className="w-12 h-12 text-primary-500" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="cosmic-card"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Goal Progress</p>
                <p className="text-3xl font-bold text-secondary-600">
                  {healthMetrics.goalProgress}%
                </p>
                <div className="cosmic-progress mt-2">
                  <div 
                    className="cosmic-progress__bar" 
                    style={{ width: `${healthMetrics.goalProgress}%` }}
                  ></div>
                </div>
              </div>
              <ArrowTrendingUpIcon className="w-12 h-12 text-secondary-500" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="cosmic-card"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Active Supplements</p>
                <p className="text-3xl font-bold text-primary-600">
                  {healthMetrics.supplementCount}
                </p>
                <p className="text-xs text-gray-500">in your stack</p>
              </div>
              <BeakerIcon className="w-12 h-12 text-primary-500" />
            </div>
          </motion.div>
        </div>

        {/* Dashboard Stats */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="cosmic-card lg:col-span-2"
          >
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Quick Stats</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-primary-600">{dashboardStats.activeGoals}</p>
                <p className="text-sm text-gray-600">Active Goals</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-secondary-600">{dashboardStats.completedGoals}</p>
                <p className="text-sm text-gray-600">Completed Goals</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-warning-600">{dashboardStats.safetyAlerts}</p>
                <p className="text-sm text-gray-600">Safety Alerts</p>
              </div>
            </div>
            
            <div className="mt-6 pt-4 border-t border-gray-200">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-600">Monthly Budget</span>
                <span className="text-sm text-gray-600">
                  ${dashboardStats.budgetUsed} / ${dashboardStats.monthlyBudget}
                </span>
              </div>
              <div className="cosmic-progress">
                <div 
                  className="cosmic-progress__bar" 
                  style={{ width: `${(dashboardStats.budgetUsed / dashboardStats.monthlyBudget) * 100}%` }}
                ></div>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="cosmic-card"
          >
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Recent Activity</h3>
            <div className="space-y-3">
              <AnimatePresence>
                {recentActivity.map((activity, index) => (
                  <motion.div
                    key={activity.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="flex items-start space-x-3 p-3 rounded-lg bg-gray-50"
                  >
                    <div 
                      className="flex-shrink-0 p-1 rounded-full"
                      style={{ backgroundColor: getSeverityColor(activity.severity) + '20' }}
                    >
                      <div style={{ color: getSeverityColor(activity.severity) }}>
                        {getActivityIcon(activity.type)}
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                      <p className="text-xs text-gray-600">{activity.description}</p>
                      <p className="text-xs text-gray-400 mt-1">
                        {activity.timestamp.toLocaleTimeString()}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          </motion.div>
        </div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.7 }}
          className="cosmic-card"
        >
          <h3 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <button className="cosmic-button cosmic-button--primary">
              <BeakerIcon className="w-5 h-5 mr-2" />
              Add Supplement
            </button>
            <button className="cosmic-button cosmic-button--secondary">
              <ChartBarIcon className="w-5 h-5 mr-2" />
              Log Health Data
            </button>
            <button className="cosmic-button cosmic-button--outline">
              <LightBulbIcon className="w-5 h-5 mr-2" />
              Get Recommendations
            </button>
            <button className="cosmic-button cosmic-button--outline">
              <ShieldCheckIcon className="w-5 h-5 mr-2" />
              Safety Check
            </button>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default CosmicDashboard;
